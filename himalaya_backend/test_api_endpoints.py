#!/usr/bin/env python3
"""
Test API Endpoints for Chat with Databases Module

This script tests the API endpoints we've created to ensure they're properly registered
and accessible.
"""

import requests
import json
import time

BASE_URL = "http://localhost:5000/api"

def test_endpoint_accessibility():
    """Test if our new endpoints are accessible"""
    print("🌐 Testing API Endpoint Accessibility")
    print("=" * 50)
    
    # Wait for server to start
    print("Waiting for server to start...")
    time.sleep(3)
    
    endpoints_to_test = [
        # Database Ingestion Routes
        ("GET", "/database-ingestion/jobs", "List ingestion jobs"),
        
        # Database Role Routes  
        ("GET", "/database-roles", "List database roles"),
        
        # Database User Role Routes
        ("GET", "/users/1/database-roles", "List user database roles"),
    ]
    
    results = []
    
    for method, endpoint, description in endpoints_to_test:
        try:
            print(f"\nTesting {method} {endpoint} - {description}")
            
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            elif method == "POST":
                response = requests.post(f"{BASE_URL}{endpoint}", json={}, timeout=5)
            
            print(f"  Status Code: {response.status_code}")
            
            if response.status_code == 401:
                print("  ✓ Endpoint accessible (requires authentication)")
                results.append((endpoint, True, "Requires auth"))
            elif response.status_code == 404:
                print("  ✗ Endpoint not found")
                results.append((endpoint, False, "Not found"))
            elif response.status_code in [200, 400, 403, 422]:
                print("  ✓ Endpoint accessible")
                results.append((endpoint, True, "Accessible"))
            else:
                print(f"  ? Unexpected status code: {response.status_code}")
                results.append((endpoint, True, f"Status {response.status_code}"))
                
        except requests.exceptions.ConnectionError:
            print("  ✗ Connection failed - server may not be running")
            results.append((endpoint, False, "Connection failed"))
        except requests.exceptions.Timeout:
            print("  ✗ Request timeout")
            results.append((endpoint, False, "Timeout"))
        except Exception as e:
            print(f"  ✗ Error: {str(e)}")
            results.append((endpoint, False, str(e)))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 API ENDPOINT TEST RESULTS")
    print("=" * 50)
    
    accessible_count = 0
    total_count = len(results)
    
    for endpoint, accessible, status in results:
        status_icon = "✓" if accessible else "✗"
        print(f"{status_icon} {endpoint:<30} {status}")
        if accessible:
            accessible_count += 1
    
    print(f"\nOverall: {accessible_count}/{total_count} endpoints accessible")
    
    if accessible_count == total_count:
        print("🎉 All endpoints are properly registered and accessible!")
    else:
        print("⚠️ Some endpoints may not be properly registered.")
    
    return accessible_count == total_count

def test_server_health():
    """Test if the server is running"""
    print("\n🏥 Testing Server Health")
    print("=" * 30)
    
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"Health check status: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running or not accessible")
        return False
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False

def main():
    """Run API endpoint tests"""
    print("🚀 Starting API Endpoint Tests")
    print("=" * 60)
    
    # Test server health first
    server_running = test_server_health()
    
    if not server_running:
        print("\n❌ Cannot test endpoints - server is not running")
        print("\n📝 To start the server:")
        print("   cd himalaya_backend")
        print("   python3 src/app.py")
        return
    
    # Test endpoint accessibility
    endpoints_working = test_endpoint_accessibility()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    if endpoints_working:
        print("✅ All tests passed!")
        print("✅ Chat with Databases API endpoints are working correctly")
    else:
        print("⚠️ Some tests failed")
        print("⚠️ Check the server logs for more details")
    
    print("\n📝 Next Steps:")
    print("1. Test with actual database connections")
    print("2. Test the complete ingestion pipeline")
    print("3. Test role creation and user assignment")
    print("4. Implement the remaining phases")

if __name__ == "__main__":
    main()
