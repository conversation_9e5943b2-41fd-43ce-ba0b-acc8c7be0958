#!/usr/bin/env python3
"""
Test Script for Chat with Databases Module

This script tests the basic functionality of our Chat with Databases implementation:
1. Database connection and schema analysis
2. EDA engine functionality
3. Semantic summary generation
4. Role management
5. API endpoints

Run this script to verify the implementation works correctly.
"""

import sys
import os
import json
import traceback
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_database_schema_analyzer():
    """Test the database schema analyzer"""
    print("\n=== Testing Database Schema Analyzer ===")
    
    try:
        from utils.database_schema_analyzer import DatabaseSchemaAnalyzer
        
        analyzer = DatabaseSchemaAnalyzer()
        print("✓ DatabaseSchemaAnalyzer imported and initialized successfully")
        
        # Test with a sample SQLite database configuration
        test_db_config = {
            'name': 'Test Database',
            'db_type': 'sqlite',
            'host': 'localhost',
            'port': 5432,
            'database_name': ':memory:',
            'username': 'test',
            'password_encrypted': 'test'
        }
        
        print("✓ Test database configuration created")
        print("  Note: Full schema analysis requires a real database connection")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing DatabaseSchemaAnalyzer: {str(e)}")
        print(traceback.format_exc())
        return False

def test_eda_engine():
    """Test the EDA engine"""
    print("\n=== Testing EDA Engine ===")
    
    try:
        from utils.database_eda_engine import DatabaseEDAEngine
        
        engine = DatabaseEDAEngine()
        print("✓ DatabaseEDAEngine imported and initialized successfully")
        
        # Test with sample data
        import pandas as pd
        sample_df = pd.DataFrame({
            'id': [1, 2, 3, 4, 5],
            'name': ['Alice', 'Bob', 'Charlie', 'David', 'Eve'],
            'age': [25, 30, 35, 28, 32],
            'salary': [50000, 60000, 70000, 55000, 65000],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']
        })
        
        # Test basic statistics
        basic_stats = engine._get_basic_statistics(sample_df)
        print(f"✓ Basic statistics generated: {len(basic_stats)} metrics")
        
        # Test column analysis
        column_analysis = engine._analyze_columns(sample_df)
        print(f"✓ Column analysis completed for {len(column_analysis)} columns")
        
        # Test data quality analysis
        data_quality = engine._analyze_data_quality(sample_df)
        print(f"✓ Data quality analysis completed: {data_quality.get('missing_percentage', 0):.1f}% missing data")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing DatabaseEDAEngine: {str(e)}")
        print(traceback.format_exc())
        return False

def test_semantic_generator():
    """Test the semantic summary generator"""
    print("\n=== Testing Semantic Summary Generator ===")
    
    try:
        from utils.database_semantic_generator import DatabaseSemanticGenerator
        
        generator = DatabaseSemanticGenerator()
        print("✓ DatabaseSemanticGenerator imported and initialized successfully")
        
        # Test context preparation
        sample_db_config = {
            'name': 'Sample HR Database',
            'db_type': 'postgresql'
        }
        
        sample_schema = {
            'tables': {
                'employees': {
                    'columns': [
                        {'name': 'id', 'type': 'INTEGER', 'primary_key': True},
                        {'name': 'name', 'type': 'VARCHAR(100)', 'nullable': False},
                        {'name': 'email', 'type': 'VARCHAR(255)', 'nullable': False},
                        {'name': 'department_id', 'type': 'INTEGER', 'nullable': True}
                    ],
                    'type': 'table'
                }
            },
            'total_columns': 4
        }
        
        sample_eda = {'success': True, 'database_summary': {'total_rows': 1000}}
        
        context = generator._prepare_database_context(sample_db_config, sample_schema, sample_eda)
        print(f"✓ Database context prepared: {len(context)} characters")
        
        # Test table context preparation
        table_context = generator._prepare_table_context(
            'employees', 
            sample_schema['tables']['employees'], 
            {}, 
            sample_db_config
        )
        print(f"✓ Table context prepared: {len(table_context)} characters")
        
        print("  Note: LLM summary generation requires OpenAI API key")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing DatabaseSemanticGenerator: {str(e)}")
        print(traceback.format_exc())
        return False

def test_column_masking_engine():
    """Test the column masking engine"""
    print("\n=== Testing Column Masking Engine ===")
    
    try:
        from utils.database_column_masking_engine import DatabaseColumnMaskingEngine
        
        engine = DatabaseColumnMaskingEngine()
        print("✓ DatabaseColumnMaskingEngine imported and initialized successfully")
        
        # Test masking functions
        test_email = "<EMAIL>"
        masked_email = engine._mask_email(test_email)
        print(f"✓ Email masking: {test_email} -> {masked_email}")
        
        test_phone = "************"
        masked_phone = engine._mask_phone(test_phone)
        print(f"✓ Phone masking: {test_phone} -> {masked_phone}")
        
        test_value = "sensitive_data_123"
        masked_value = engine._mask_value(test_value)
        print(f"✓ Default masking: {test_value} -> {masked_value}")
        
        hashed_value = engine._hash_value(test_value)
        print(f"✓ Hash masking: {test_value} -> {hashed_value}")
        
        # Test with sample data
        sample_data = [
            {'id': 1, 'name': 'John Doe', 'email': '<EMAIL>', 'phone': '************'},
            {'id': 2, 'name': 'Jane Smith', 'email': '<EMAIL>', 'phone': '************'}
        ]
        
        masking_rules = {
            'users': {
                'email': 'email_mask',
                'phone': 'phone_mask'
            }
        }
        
        masked_data = engine.apply_masking_to_results(sample_data, 'users', masking_rules)
        print(f"✓ Applied masking to {len(masked_data)} rows")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing DatabaseColumnMaskingEngine: {str(e)}")
        print(traceback.format_exc())
        return False

def test_role_schema_generator():
    """Test the role schema generator"""
    print("\n=== Testing Role Schema Generator ===")
    
    try:
        from utils.database_role_schema_generator import DatabaseRoleSchemaGenerator
        
        generator = DatabaseRoleSchemaGenerator()
        print("✓ DatabaseRoleSchemaGenerator imported and initialized successfully")
        
        # Create a mock role object
        class MockRole:
            def __init__(self):
                self.id = 1
                self.name = "Test Role"
                self.external_database_id = 1
                self.allowed_tables = ['employees', 'departments']
                self.restricted_tables = ['salaries']
                self.column_masking_rules = {
                    'employees': {
                        'email': 'email_mask',
                        'phone': 'phone_mask'
                    }
                }
                self.where_conditions = "WHERE department = 'Sales'"
                self.role_schema = None
        
        mock_role = MockRole()
        
        # Test table accessibility
        is_accessible = generator._is_table_accessible('employees', mock_role)
        print(f"✓ Table accessibility check: employees -> {is_accessible}")
        
        is_restricted = generator._is_table_accessible('salaries', mock_role)
        print(f"✓ Table accessibility check: salaries -> {is_restricted}")
        
        print("  Note: Full schema generation requires database models")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing DatabaseRoleSchemaGenerator: {str(e)}")
        print(traceback.format_exc())
        return False

def test_ingestion_orchestrator():
    """Test the ingestion orchestrator"""
    print("\n=== Testing Ingestion Orchestrator ===")
    
    try:
        from utils.database_ingestion_orchestrator import DatabaseIngestionOrchestrator
        
        orchestrator = DatabaseIngestionOrchestrator()
        print("✓ DatabaseIngestionOrchestrator imported and initialized successfully")
        print("  Note: Full ingestion testing requires database setup and models")
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing DatabaseIngestionOrchestrator: {str(e)}")
        print(traceback.format_exc())
        return False

def test_api_imports():
    """Test that all API modules can be imported"""
    print("\n=== Testing API Module Imports ===")
    
    api_modules = [
        'api.database_ingestion_routes',
        'api.database_role_routes', 
        'api.database_user_role_routes'
    ]
    
    success_count = 0
    
    for module_name in api_modules:
        try:
            __import__(module_name)
            print(f"✓ {module_name} imported successfully")
            success_count += 1
        except Exception as e:
            print(f"✗ Error importing {module_name}: {str(e)}")
    
    print(f"✓ {success_count}/{len(api_modules)} API modules imported successfully")
    return success_count == len(api_modules)

def test_models_import():
    """Test that new models can be imported"""
    print("\n=== Testing Model Imports ===")
    
    try:
        from models.models import (
            DatabaseRole, DatabaseSchema, DatabaseTable, DatabaseEDA,
            DatabaseSemanticSummary, TrainingSession, TrainingInteraction,
            DatabaseMemoryContext, UserMemoryContext, MemoryUpdate,
            EnhancedDatabaseChatSession, EnhancedDatabaseChatMessage,
            DatabaseIngestionJob, SystemConfiguration
        )
        
        print("✓ All new database models imported successfully")
        
        # Test model attributes
        role_attrs = ['name', 'description', 'external_database_id', 'where_conditions']
        for attr in role_attrs:
            if hasattr(DatabaseRole, attr):
                print(f"  ✓ DatabaseRole.{attr} exists")
            else:
                print(f"  ✗ DatabaseRole.{attr} missing")
        
        return True
        
    except Exception as e:
        print(f"✗ Error importing models: {str(e)}")
        print(traceback.format_exc())
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Chat with Databases Module Tests")
    print("=" * 60)
    
    tests = [
        ("Model Imports", test_models_import),
        ("API Module Imports", test_api_imports),
        ("Database Schema Analyzer", test_database_schema_analyzer),
        ("EDA Engine", test_eda_engine),
        ("Semantic Generator", test_semantic_generator),
        ("Column Masking Engine", test_column_masking_engine),
        ("Role Schema Generator", test_role_schema_generator),
        ("Ingestion Orchestrator", test_ingestion_orchestrator)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n✗ Unexpected error in {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:<8} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 All tests passed! The Chat with Databases module is ready for further testing.")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
    
    print("\n📝 Next Steps:")
    print("1. Run database migrations to create the new tables")
    print("2. Set up a test database for full integration testing")
    print("3. Configure OpenAI API key for semantic summary generation")
    print("4. Test the API endpoints with a REST client")
    print("5. Implement the remaining phases (Training Interface, Agentic Chat, Memory Management)")

if __name__ == "__main__":
    main()
