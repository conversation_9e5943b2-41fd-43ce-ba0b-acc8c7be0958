"""
Database Summary Editor API Routes

This module provides REST API endpoints for trainers to edit semantic summaries:
- View and edit database, table, and column summaries
- Track summary versions and changes
- Approve and publish summary updates
"""

from flask import Blueprint, request, jsonify
from flask_cors import CORS
import logging
import traceback
from datetime import datetime
from sqlalchemy import and_, or_

from models.models import (
    db, DatabaseRole, DatabaseSemanticSummary, User, role_trainers,
    ExternalDatabase, DatabaseTable, DatabaseSchema
)
from utils.decorators import login_required, require_scope
from utils.request_utils import get_request_json

logger = logging.getLogger(__name__)

# Create blueprint
database_summary_editor_bp = Blueprint('database_summary_editor', __name__)
CORS(database_summary_editor_bp)

@database_summary_editor_bp.route('/training/summaries', methods=['GET'])
@login_required
def list_editable_summaries():
    """
    List semantic summaries that the user can edit
    """
    try:
        # Get query parameters
        role_id = request.args.get('role_id', type=int)
        summary_type = request.args.get('type')  # database, table, column
        search = request.args.get('search', '')
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build query
        query = DatabaseSemanticSummary.query.join(ExternalDatabase)
        
        # Filter by user access (trainers can only edit summaries for roles they train)
        if not request.user.is_admin:
            # Get roles the user can train
            trainable_roles = db.session.query(role_trainers.c.database_role_id).filter_by(
                user_id=request.user.id,
                is_active=True
            ).subquery()
            
            query = query.filter(
                or_(
                    DatabaseSemanticSummary.database_role_id.in_(trainable_roles),
                    DatabaseSemanticSummary.database_role_id.is_(None)  # Database-level summaries
                )
            )
        
        # Apply filters
        if role_id:
            query = query.filter(DatabaseSemanticSummary.database_role_id == role_id)
        
        if summary_type:
            query = query.filter(DatabaseSemanticSummary.summary_type == summary_type)
        
        if search:
            query = query.filter(
                or_(
                    DatabaseSemanticSummary.target_name.ilike(f'%{search}%'),
                    DatabaseSemanticSummary.current_summary.ilike(f'%{search}%')
                )
            )
        
        # Only active summaries
        query = query.filter(DatabaseSemanticSummary.is_active == True)
        
        # Order by most recent first
        query = query.order_by(DatabaseSemanticSummary.updated_at.desc())
        
        # Apply pagination
        total_count = query.count()
        summaries = query.offset(offset).limit(limit).all()
        
        # Format response
        summaries_data = []
        for summary in summaries:
            summary_data = {
                'id': summary.id,
                'database_id': summary.external_database_id,
                'database_name': summary.database.name,
                'role_id': summary.database_role_id,
                'role_name': summary.role.name if summary.role else None,
                'summary_type': summary.summary_type,
                'target_name': summary.target_name,
                'current_summary': summary.current_summary[:200] + '...' if len(summary.current_summary) > 200 else summary.current_summary,
                'has_human_edits': bool(summary.human_edited_summary),
                'token_count': summary.token_count,
                'version': summary.version,
                'created_at': summary.created_at.isoformat() if summary.created_at else None,
                'updated_at': summary.updated_at.isoformat() if summary.updated_at else None,
                'last_edited_by': summary.editor.user_name if summary.editor else None
            }
            summaries_data.append(summary_data)
        
        return jsonify({
            'success': True,
            'summaries': summaries_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing editable summaries: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_summary_editor_bp.route('/training/summaries/<int:summary_id>', methods=['GET'])
@login_required
def get_summary_for_editing():
    """Get detailed summary information for editing"""
    try:
        summary_id = request.view_args['summary_id']
        
        summary = DatabaseSemanticSummary.query.get(summary_id)
        if not summary:
            return jsonify({'error': 'Summary not found'}), 404
        
        # Check if user can edit this summary
        can_edit = False
        if request.user.is_admin:
            can_edit = True
        elif summary.database_role_id:
            # Check if user is a trainer for this role
            is_trainer = db.session.query(role_trainers).filter_by(
                user_id=request.user.id,
                database_role_id=summary.database_role_id,
                is_active=True
            ).first()
            can_edit = bool(is_trainer)
        else:
            # Database-level summary - check if user trains any role for this database
            trainable_roles = db.session.query(role_trainers).join(DatabaseRole).filter(
                role_trainers.c.user_id == request.user.id,
                role_trainers.c.is_active == True,
                DatabaseRole.external_database_id == summary.external_database_id
            ).first()
            can_edit = bool(trainable_roles)
        
        if not can_edit:
            return jsonify({'error': 'You are not authorized to edit this summary'}), 403
        
        # Get additional context
        context_data = {}
        
        if summary.table_id:
            table = DatabaseTable.query.get(summary.table_id)
            if table:
                context_data['table_info'] = {
                    'name': table.table_name,
                    'type': table.table_type,
                    'row_count': table.row_count,
                    'column_count': table.column_count
                }
        
        summary_data = {
            'id': summary.id,
            'database_id': summary.external_database_id,
            'database_name': summary.database.name,
            'role_id': summary.database_role_id,
            'role_name': summary.role.name if summary.role else None,
            'table_id': summary.table_id,
            'summary_type': summary.summary_type,
            'target_name': summary.target_name,
            'llm_generated_summary': summary.llm_generated_summary,
            'human_edited_summary': summary.human_edited_summary,
            'current_summary': summary.current_summary,
            'business_description': summary.business_description,
            'usage_patterns': summary.usage_patterns,
            'data_lineage': summary.data_lineage,
            'quality_notes': summary.quality_notes,
            'token_count': summary.token_count,
            'version': summary.version,
            'is_active': summary.is_active,
            'created_at': summary.created_at.isoformat() if summary.created_at else None,
            'updated_at': summary.updated_at.isoformat() if summary.updated_at else None,
            'created_by': summary.creator.user_name if summary.creator else 'System',
            'last_edited_by': summary.editor.user_name if summary.editor else None,
            'context': context_data
        }
        
        return jsonify({
            'success': True,
            'summary': summary_data
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting summary for editing: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_summary_editor_bp.route('/training/summaries/<int:summary_id>', methods=['PUT'])
@login_required
def update_summary():
    """
    Update a semantic summary
    
    Request Body:
    {
        "human_edited_summary": "Updated summary text...",
        "business_description": "Business context...",
        "usage_patterns": "How this data is used...",
        "data_lineage": "Where this data comes from...",
        "quality_notes": "Data quality observations..."
    }
    """
    try:
        summary_id = request.view_args['summary_id']
        
        summary = DatabaseSemanticSummary.query.get(summary_id)
        if not summary:
            return jsonify({'error': 'Summary not found'}), 404
        
        # Check if user can edit this summary (same logic as get_summary_for_editing)
        can_edit = False
        if request.user.is_admin:
            can_edit = True
        elif summary.database_role_id:
            is_trainer = db.session.query(role_trainers).filter_by(
                user_id=request.user.id,
                database_role_id=summary.database_role_id,
                is_active=True
            ).first()
            can_edit = bool(is_trainer)
        else:
            trainable_roles = db.session.query(role_trainers).join(DatabaseRole).filter(
                role_trainers.c.user_id == request.user.id,
                role_trainers.c.is_active == True,
                DatabaseRole.external_database_id == summary.external_database_id
            ).first()
            can_edit = bool(trainable_roles)
        
        if not can_edit:
            return jsonify({'error': 'You are not authorized to edit this summary'}), 403
        
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Track if any changes were made
        changes_made = False
        
        # Update editable fields
        if 'human_edited_summary' in data:
            summary.human_edited_summary = data['human_edited_summary']
            # Update current summary to use human-edited version
            summary.current_summary = data['human_edited_summary']
            changes_made = True
        
        if 'business_description' in data:
            summary.business_description = data['business_description']
            changes_made = True
        
        if 'usage_patterns' in data:
            summary.usage_patterns = data['usage_patterns']
            changes_made = True
        
        if 'data_lineage' in data:
            summary.data_lineage = data['data_lineage']
            changes_made = True
        
        if 'quality_notes' in data:
            summary.quality_notes = data['quality_notes']
            changes_made = True
        
        if changes_made:
            # Update metadata
            summary.last_edited_by = request.user.id
            summary.updated_at = datetime.utcnow()
            summary.version += 1
            
            # Update token count if summary was changed
            if 'human_edited_summary' in data:
                summary.token_count = len(summary.current_summary.split())
            
            db.session.commit()
            
            logger.info(f"Updated summary {summary_id} by trainer {request.user.user_name}")
            
            return jsonify({
                'success': True,
                'message': 'Summary updated successfully',
                'summary': {
                    'id': summary.id,
                    'version': summary.version,
                    'token_count': summary.token_count,
                    'updated_at': summary.updated_at.isoformat() if summary.updated_at else None,
                    'last_edited_by': request.user.user_name
                }
            }), 200
        else:
            return jsonify({
                'success': True,
                'message': 'No changes made'
            }), 200
        
    except Exception as e:
        logger.error(f"Error updating summary: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_summary_editor_bp.route('/training/summaries/<int:summary_id>/revert', methods=['POST'])
@login_required
def revert_summary_to_llm():
    """Revert a summary back to the original LLM-generated version"""
    try:
        summary_id = request.view_args['summary_id']
        
        summary = DatabaseSemanticSummary.query.get(summary_id)
        if not summary:
            return jsonify({'error': 'Summary not found'}), 404
        
        # Check edit permissions (same logic as update_summary)
        can_edit = False
        if request.user.is_admin:
            can_edit = True
        elif summary.database_role_id:
            is_trainer = db.session.query(role_trainers).filter_by(
                user_id=request.user.id,
                database_role_id=summary.database_role_id,
                is_active=True
            ).first()
            can_edit = bool(is_trainer)
        else:
            trainable_roles = db.session.query(role_trainers).join(DatabaseRole).filter(
                role_trainers.c.user_id == request.user.id,
                role_trainers.c.is_active == True,
                DatabaseRole.external_database_id == summary.external_database_id
            ).first()
            can_edit = bool(trainable_roles)
        
        if not can_edit:
            return jsonify({'error': 'You are not authorized to edit this summary'}), 403
        
        # Revert to LLM-generated summary
        summary.current_summary = summary.llm_generated_summary
        summary.human_edited_summary = None
        summary.last_edited_by = request.user.id
        summary.updated_at = datetime.utcnow()
        summary.version += 1
        summary.token_count = len(summary.llm_generated_summary.split())
        
        db.session.commit()
        
        logger.info(f"Reverted summary {summary_id} to LLM version by trainer {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Summary reverted to LLM-generated version',
            'summary': {
                'id': summary.id,
                'version': summary.version,
                'current_summary': summary.current_summary,
                'updated_at': summary.updated_at.isoformat() if summary.updated_at else None
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error reverting summary: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_summary_editor_bp.route('/training/summaries/bulk-update', methods=['POST'])
@login_required
def bulk_update_summaries():
    """
    Bulk update multiple summaries
    
    Request Body:
    {
        "updates": [
            {
                "summary_id": 1,
                "human_edited_summary": "Updated summary...",
                "business_description": "Business context..."
            },
            {
                "summary_id": 2,
                "human_edited_summary": "Another updated summary..."
            }
        ]
    }
    """
    try:
        data = get_request_json()
        if not data or 'updates' not in data:
            return jsonify({'error': 'No updates provided'}), 400
        
        updates = data['updates']
        if not isinstance(updates, list):
            return jsonify({'error': 'Updates must be a list'}), 400
        
        results = []
        errors = []
        
        for update in updates:
            try:
                summary_id = update.get('summary_id')
                if not summary_id:
                    errors.append({'summary_id': None, 'error': 'summary_id is required'})
                    continue
                
                summary = DatabaseSemanticSummary.query.get(summary_id)
                if not summary:
                    errors.append({'summary_id': summary_id, 'error': 'Summary not found'})
                    continue
                
                # Check edit permissions
                can_edit = False
                if request.user.is_admin:
                    can_edit = True
                elif summary.database_role_id:
                    is_trainer = db.session.query(role_trainers).filter_by(
                        user_id=request.user.id,
                        database_role_id=summary.database_role_id,
                        is_active=True
                    ).first()
                    can_edit = bool(is_trainer)
                else:
                    trainable_roles = db.session.query(role_trainers).join(DatabaseRole).filter(
                        role_trainers.c.user_id == request.user.id,
                        role_trainers.c.is_active == True,
                        DatabaseRole.external_database_id == summary.external_database_id
                    ).first()
                    can_edit = bool(trainable_roles)
                
                if not can_edit:
                    errors.append({'summary_id': summary_id, 'error': 'Access denied'})
                    continue
                
                # Apply updates
                changes_made = False
                
                if 'human_edited_summary' in update:
                    summary.human_edited_summary = update['human_edited_summary']
                    summary.current_summary = update['human_edited_summary']
                    changes_made = True
                
                if 'business_description' in update:
                    summary.business_description = update['business_description']
                    changes_made = True
                
                if 'usage_patterns' in update:
                    summary.usage_patterns = update['usage_patterns']
                    changes_made = True
                
                if 'data_lineage' in update:
                    summary.data_lineage = update['data_lineage']
                    changes_made = True
                
                if 'quality_notes' in update:
                    summary.quality_notes = update['quality_notes']
                    changes_made = True
                
                if changes_made:
                    summary.last_edited_by = request.user.id
                    summary.updated_at = datetime.utcnow()
                    summary.version += 1
                    
                    if 'human_edited_summary' in update:
                        summary.token_count = len(summary.current_summary.split())
                
                results.append({
                    'summary_id': summary_id,
                    'success': True,
                    'changes_made': changes_made,
                    'version': summary.version
                })
                
            except Exception as e:
                errors.append({
                    'summary_id': update.get('summary_id'),
                    'error': str(e)
                })
        
        # Commit all changes
        db.session.commit()
        
        logger.info(f"Bulk updated {len(results)} summaries by trainer {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': f'Bulk update completed: {len(results)} successful, {len(errors)} errors',
            'results': results,
            'errors': errors
        }), 200
        
    except Exception as e:
        logger.error(f"Error in bulk update summaries: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500
