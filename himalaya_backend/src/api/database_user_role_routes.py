"""
Database User Role Assignment API Routes

This module provides REST API endpoints for managing user role assignments:
- Assign users to database roles
- Manage role permissions and expiration
- Assign trainers to roles
- Monitor role usage
"""

from flask import Blueprint, request, jsonify
from flask_cors import CORS
import logging
import traceback
from datetime import datetime, timedelta
from sqlalchemy import and_, or_, text

from models.models import (
    db, DatabaseRole, User, user_database_roles, role_trainers,
    ExternalDatabase, EnhancedDatabaseChatSession
)
from utils.decorators import login_required, require_scope
from utils.request_utils import get_request_json

logger = logging.getLogger(__name__)

# Create blueprint
database_user_role_bp = Blueprint('database_user_role', __name__)
CORS(database_user_role_bp)

@database_user_role_bp.route('/database-roles/<int:role_id>/users', methods=['POST'])
@login_required
def assign_user_to_role(role_id):
    """
    Assign a user to a database role
    
    Request Body:
    {
        "user_id": 123,
        "expires_at": "2024-12-31T23:59:59Z"  // Optional
    }
    """
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check if user has permission to assign users to this role
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        user_id = data.get('user_id')
        if not user_id:
            return jsonify({'error': 'user_id is required'}), 400
        
        # Check if user exists
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Check if user is already assigned to this role
        existing_assignment = db.session.execute(
            text("""
                SELECT id FROM user_database_roles 
                WHERE user_id = :user_id AND database_role_id = :role_id AND is_active = true
            """),
            {'user_id': user_id, 'role_id': role_id}
        ).fetchone()
        
        if existing_assignment:
            return jsonify({'error': 'User is already assigned to this role'}), 409
        
        # Parse expiration date if provided
        expires_at = None
        if data.get('expires_at'):
            try:
                expires_at = datetime.fromisoformat(data['expires_at'].replace('Z', '+00:00'))
            except ValueError:
                return jsonify({'error': 'Invalid expires_at format. Use ISO format.'}), 400
        
        # Create the assignment
        assignment_data = {
            'user_id': user_id,
            'database_role_id': role_id,
            'assigned_by': request.user.id,
            'assigned_at': datetime.utcnow(),
            'expires_at': expires_at,
            'is_active': True
        }
        
        db.session.execute(
            text("""
                INSERT INTO user_database_roles (user_id, database_role_id, assigned_by, assigned_at, expires_at, is_active)
                VALUES (:user_id, :database_role_id, :assigned_by, :assigned_at, :expires_at, :is_active)
            """),
            assignment_data
        )
        db.session.commit()
        
        logger.info(f"Assigned user {user.user_name} to role {role.name} by {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': f'User {user.user_name} assigned to role {role.name}',
            'assignment': {
                'user_id': user_id,
                'user_name': user.user_name,
                'role_id': role_id,
                'role_name': role.name,
                'assigned_by': request.user.user_name,
                'assigned_at': assignment_data['assigned_at'].isoformat(),
                'expires_at': expires_at.isoformat() if expires_at else None
            }
        }), 201
        
    except Exception as e:
        logger.error(f"Error assigning user to role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_user_role_bp.route('/database-roles/<int:role_id>/users/<int:user_id>', methods=['DELETE'])
@login_required
def remove_user_from_role(role_id, user_id):
    """Remove a user from a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check permissions
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Find the assignment
        assignment = db.session.execute(
            text("""
                SELECT id FROM user_database_roles 
                WHERE user_id = :user_id AND database_role_id = :role_id AND is_active = true
            """),
            {'user_id': user_id, 'role_id': role_id}
        ).fetchone()
        
        if not assignment:
            return jsonify({'error': 'User assignment not found'}), 404
        
        # Deactivate the assignment
        db.session.execute(
            text("""
                UPDATE user_database_roles 
                SET is_active = false 
                WHERE id = :assignment_id
            """),
            {'assignment_id': assignment[0]}
        )
        db.session.commit()
        
        user = User.query.get(user_id)
        logger.info(f"Removed user {user.user_name if user else user_id} from role {role.name} by {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'User removed from role successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Error removing user from role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_user_role_bp.route('/database-roles/<int:role_id>/users', methods=['GET'])
@login_required
def list_role_users(role_id):
    """List users assigned to a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check permissions
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Get query parameters
        include_expired = request.args.get('include_expired', 'false').lower() == 'true'
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build query
        query = """
            SELECT 
                udr.id as assignment_id,
                u.id as user_id,
                u.user_name,
                u.email,
                udr.assigned_at,
                udr.expires_at,
                udr.is_active,
                assigner.user_name as assigned_by
            FROM user_database_roles udr
            JOIN users u ON udr.user_id = u.id
            LEFT JOIN users assigner ON udr.assigned_by = assigner.id
            WHERE udr.database_role_id = :role_id
        """
        
        params = {'role_id': role_id}
        
        if not include_expired:
            query += " AND udr.is_active = true AND (udr.expires_at IS NULL OR udr.expires_at > :now)"
            params['now'] = datetime.utcnow()
        
        query += " ORDER BY udr.assigned_at DESC LIMIT :limit OFFSET :offset"
        params.update({'limit': limit, 'offset': offset})
        
        # Get total count
        count_query = """
            SELECT COUNT(*) 
            FROM user_database_roles udr
            WHERE udr.database_role_id = :role_id
        """
        count_params = {'role_id': role_id}
        
        if not include_expired:
            count_query += " AND udr.is_active = true AND (udr.expires_at IS NULL OR udr.expires_at > :now)"
            count_params['now'] = datetime.utcnow()
        
        total_count = db.session.execute(text(count_query), count_params).scalar()
        assignments = db.session.execute(text(query), params).fetchall()
        
        # Format response
        users_data = []
        for assignment in assignments:
            user_data = {
                'assignment_id': assignment.assignment_id,
                'user_id': assignment.user_id,
                'user_name': assignment.user_name,
                'email': assignment.email,
                'assigned_at': assignment.assigned_at.isoformat() if assignment.assigned_at else None,
                'expires_at': assignment.expires_at.isoformat() if assignment.expires_at else None,
                'is_active': assignment.is_active,
                'assigned_by': assignment.assigned_by,
                'is_expired': assignment.expires_at and assignment.expires_at < datetime.utcnow() if assignment.expires_at else False
            }
            users_data.append(user_data)
        
        return jsonify({
            'success': True,
            'role_id': role_id,
            'role_name': role.name,
            'users': users_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing role users: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_user_role_bp.route('/database-roles/<int:role_id>/trainers', methods=['POST'])
@login_required
def assign_trainer_to_role(role_id):
    """
    Assign a trainer to a database role
    
    Request Body:
    {
        "user_id": 123
    }
    """
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check permissions
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        user_id = data.get('user_id')
        if not user_id:
            return jsonify({'error': 'user_id is required'}), 400
        
        # Check if user exists
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Check if user is already a trainer for this role
        existing_trainer = db.session.execute(
            text("""
                SELECT id FROM role_trainers 
                WHERE user_id = :user_id AND database_role_id = :role_id AND is_active = true
            """),
            {'user_id': user_id, 'role_id': role_id}
        ).fetchone()
        
        if existing_trainer:
            return jsonify({'error': 'User is already a trainer for this role'}), 409
        
        # Create the trainer assignment
        trainer_data = {
            'user_id': user_id,
            'database_role_id': role_id,
            'assigned_by': request.user.id,
            'assigned_at': datetime.utcnow(),
            'is_active': True
        }
        
        db.session.execute(
            text("""
                INSERT INTO role_trainers (user_id, database_role_id, assigned_by, assigned_at, is_active)
                VALUES (:user_id, :database_role_id, :assigned_by, :assigned_at, :is_active)
            """),
            trainer_data
        )
        db.session.commit()
        
        logger.info(f"Assigned trainer {user.user_name} to role {role.name} by {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': f'User {user.user_name} assigned as trainer for role {role.name}',
            'assignment': {
                'user_id': user_id,
                'user_name': user.user_name,
                'role_id': role_id,
                'role_name': role.name,
                'assigned_by': request.user.user_name,
                'assigned_at': trainer_data['assigned_at'].isoformat()
            }
        }), 201
        
    except Exception as e:
        logger.error(f"Error assigning trainer to role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_user_role_bp.route('/database-roles/<int:role_id>/trainers/<int:user_id>', methods=['DELETE'])
@login_required
def remove_trainer_from_role(role_id, user_id):
    """Remove a trainer from a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check permissions
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Find the trainer assignment
        trainer_assignment = db.session.execute(
            text("""
                SELECT id FROM role_trainers 
                WHERE user_id = :user_id AND database_role_id = :role_id AND is_active = true
            """),
            {'user_id': user_id, 'role_id': role_id}
        ).fetchone()
        
        if not trainer_assignment:
            return jsonify({'error': 'Trainer assignment not found'}), 404
        
        # Deactivate the trainer assignment
        db.session.execute(
            text("""
                UPDATE role_trainers 
                SET is_active = false 
                WHERE id = :assignment_id
            """),
            {'assignment_id': trainer_assignment[0]}
        )
        db.session.commit()
        
        user = User.query.get(user_id)
        logger.info(f"Removed trainer {user.user_name if user else user_id} from role {role.name} by {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Trainer removed from role successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Error removing trainer from role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_user_role_bp.route('/database-roles/<int:role_id>/trainers', methods=['GET'])
@login_required
def list_role_trainers(role_id):
    """List trainers assigned to a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check permissions
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Get trainers
        query = """
            SELECT 
                rt.id as assignment_id,
                u.id as user_id,
                u.user_name,
                u.email,
                rt.assigned_at,
                rt.is_active,
                assigner.user_name as assigned_by
            FROM role_trainers rt
            JOIN users u ON rt.user_id = u.id
            LEFT JOIN users assigner ON rt.assigned_by = assigner.id
            WHERE rt.database_role_id = :role_id AND rt.is_active = true
            ORDER BY rt.assigned_at DESC
        """
        
        trainers = db.session.execute(text(query), {'role_id': role_id}).fetchall()
        
        # Format response
        trainers_data = []
        for trainer in trainers:
            trainer_data = {
                'assignment_id': trainer.assignment_id,
                'user_id': trainer.user_id,
                'user_name': trainer.user_name,
                'email': trainer.email,
                'assigned_at': trainer.assigned_at.isoformat() if trainer.assigned_at else None,
                'assigned_by': trainer.assigned_by
            }
            trainers_data.append(trainer_data)
        
        return jsonify({
            'success': True,
            'role_id': role_id,
            'role_name': role.name,
            'trainers': trainers_data,
            'total_trainers': len(trainers_data)
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing role trainers: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_user_role_bp.route('/users/<int:user_id>/database-roles', methods=['GET'])
@login_required
def list_user_database_roles(user_id):
    """List database roles assigned to a user"""
    try:
        # Check if user can view this information
        if not request.user.is_admin and request.user.id != user_id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        user = User.query.get(user_id)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Get user's role assignments
        query = """
            SELECT 
                udr.id as assignment_id,
                dr.id as role_id,
                dr.name as role_name,
                dr.description as role_description,
                dr.is_published,
                dr.training_status,
                ed.id as database_id,
                ed.name as database_name,
                ed.db_type,
                udr.assigned_at,
                udr.expires_at,
                udr.is_active,
                assigner.user_name as assigned_by
            FROM user_database_roles udr
            JOIN database_roles dr ON udr.database_role_id = dr.id
            JOIN external_databases ed ON dr.external_database_id = ed.id
            LEFT JOIN users assigner ON udr.assigned_by = assigner.id
            WHERE udr.user_id = :user_id AND udr.is_active = true
            AND (udr.expires_at IS NULL OR udr.expires_at > :now)
            ORDER BY udr.assigned_at DESC
        """
        
        roles = db.session.execute(text(query), {
            'user_id': user_id,
            'now': datetime.utcnow()
        }).fetchall()
        
        # Format response
        roles_data = []
        for role in roles:
            role_data = {
                'assignment_id': role.assignment_id,
                'role_id': role.role_id,
                'role_name': role.role_name,
                'role_description': role.role_description,
                'is_published': role.is_published,
                'training_status': role.training_status,
                'database_id': role.database_id,
                'database_name': role.database_name,
                'database_type': role.db_type,
                'assigned_at': role.assigned_at.isoformat() if role.assigned_at else None,
                'expires_at': role.expires_at.isoformat() if role.expires_at else None,
                'assigned_by': role.assigned_by
            }
            roles_data.append(role_data)
        
        return jsonify({
            'success': True,
            'user_id': user_id,
            'user_name': user.user_name,
            'roles': roles_data,
            'total_roles': len(roles_data)
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing user database roles: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_user_role_bp.route('/database-roles/<int:role_id>/usage-stats', methods=['GET'])
@login_required
def get_role_usage_stats(role_id):
    """Get usage statistics for a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check permissions
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Get time range from query parameters
        days = request.args.get('days', 30, type=int)
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Get active users count
        active_users_count = db.session.execute(
            text("""
                SELECT COUNT(*) FROM user_database_roles 
                WHERE database_role_id = :role_id AND is_active = true
                AND (expires_at IS NULL OR expires_at > :now)
            """),
            {'role_id': role_id, 'now': datetime.utcnow()}
        ).scalar()
        
        # Get chat sessions count
        chat_sessions_count = EnhancedDatabaseChatSession.query.filter_by(
            database_role_id=role_id
        ).filter(
            EnhancedDatabaseChatSession.created_at >= start_date
        ).count()
        
        # Get active chat sessions (recent activity)
        active_sessions_count = EnhancedDatabaseChatSession.query.filter_by(
            database_role_id=role_id,
            is_active=True
        ).filter(
            EnhancedDatabaseChatSession.last_activity_at >= start_date
        ).count()
        
        stats = {
            'role_id': role_id,
            'role_name': role.name,
            'time_period_days': days,
            'active_users': active_users_count,
            'chat_sessions_created': chat_sessions_count,
            'active_chat_sessions': active_sessions_count,
            'is_published': role.is_published,
            'training_status': role.training_status
        }
        
        return jsonify({
            'success': True,
            'stats': stats
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting role usage stats: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
