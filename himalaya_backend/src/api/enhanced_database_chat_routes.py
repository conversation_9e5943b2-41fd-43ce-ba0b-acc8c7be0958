"""
Enhanced Database Chat API Routes

This module provides REST API endpoints for the enhanced database chat system:
- Start and manage enhanced chat sessions with memory
- Process queries through the agentic system
- Manage memory contexts and chat history
"""

from flask import Blueprint, request, jsonify
from flask_cors import CORS
import logging
import traceback
from datetime import datetime
from sqlalchemy import and_, or_

from models.models import (
    db, EnhancedDatabaseChatSession, EnhancedDatabaseChatMessage,
    DatabaseRole, User, user_database_roles
)
from utils.decorators import login_required, require_scope
from utils.request_utils import get_request_json
from utils.database_memory_manager import DatabaseMemoryManager
from agents.database_chat.agentic_chat_orchestrator import AgenticDatabaseChatOrchestrator

logger = logging.getLogger(__name__)

# Create blueprint
enhanced_database_chat_bp = Blueprint('enhanced_database_chat', __name__)
CORS(enhanced_database_chat_bp)

# Initialize components
memory_manager = DatabaseMemoryManager()
chat_orchestrator = AgenticDatabaseChatOrchestrator()

@enhanced_database_chat_bp.route('/enhanced-chat/sessions', methods=['POST'])
@login_required
def create_chat_session():
    """
    Create a new enhanced database chat session
    
    Request Body:
    {
        "database_role_id": 1,
        "session_name": "Sales Analysis Session",
        "session_description": "Analyzing Q4 sales performance"
    }
    """
    try:
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        database_role_id = data.get('database_role_id')
        if not database_role_id:
            return jsonify({'error': 'database_role_id is required'}), 400
        
        # Check if role exists and user has access
        role = DatabaseRole.query.get(database_role_id)
        if not role:
            return jsonify({'error': 'Database role not found'}), 404
        
        # Check if user is assigned to this role
        user_assignment = db.session.query(user_database_roles).filter_by(
            user_id=request.user.id,
            database_role_id=database_role_id,
            is_active=True
        ).first()
        
        if not user_assignment and not request.user.is_admin:
            return jsonify({'error': 'You are not assigned to this database role'}), 403
        
        # Get or create memory contexts
        memory_context = memory_manager.get_memory_context_for_chat(
            request.user.id, database_role_id
        )
        
        # Create the chat session
        session = EnhancedDatabaseChatSession(
            user_id=request.user.id,
            database_role_id=database_role_id,
            session_name=data.get('session_name'),
            session_description=data.get('session_description'),
            user_memory_context_id=memory_context['user_memory_id'],
            database_memory_context_id=memory_context['database_memory_id'],
            is_active=True
        )
        
        db.session.add(session)
        db.session.commit()
        
        logger.info(f"Created enhanced chat session {session.id} for user {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Enhanced chat session created successfully',
            'session': {
                'id': session.id,
                'role_id': database_role_id,
                'role_name': role.name,
                'session_name': session.session_name,
                'session_description': session.session_description,
                'created_at': session.created_at.isoformat() if session.created_at else None,
                'memory_context': {
                    'user_context_summary': _summarize_memory_context(memory_context['user_context']),
                    'database_context_summary': _summarize_memory_context(memory_context['database_context'])
                }
            }
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating chat session: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@enhanced_database_chat_bp.route('/enhanced-chat/sessions/<int:session_id>/messages', methods=['POST'])
@login_required
def send_message():
    """
    Send a message in an enhanced chat session
    
    Request Body:
    {
        "question": "Show me sales data for Q4 2024"
    }
    """
    try:
        session_id = request.view_args['session_id']
        
        # Check if session exists and user has access
        session = EnhancedDatabaseChatSession.query.get(session_id)
        if not session:
            return jsonify({'error': 'Chat session not found'}), 404
        
        if session.user_id != request.user.id and not request.user.is_admin:
            return jsonify({'error': 'Access denied to this chat session'}), 403
        
        if not session.is_active:
            return jsonify({'error': 'Chat session is not active'}), 400
        
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        question = data.get('question')
        if not question:
            return jsonify({'error': 'question is required'}), 400
        
        # Get role and memory context
        role = session.role
        memory_context = memory_manager.get_memory_context_for_chat(
            session.user_id, session.database_role_id
        )
        
        # Get recent chat history for context
        recent_messages = EnhancedDatabaseChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(EnhancedDatabaseChatMessage.created_at.desc()).limit(5).all()
        
        chat_history = []
        for msg in reversed(recent_messages):  # Reverse to get chronological order
            chat_history.append({
                'question': msg.question,
                'answer': msg.answer,
                'created_at': msg.created_at.isoformat() if msg.created_at else None
            })
        
        # Process the query through the agentic system
        user_context = {
            'user_id': session.user_id,
            'session_id': session_id,
            'memory_context': memory_context
        }
        
        logger.info(f"Processing query through agentic system: {question[:100]}...")
        processing_result = chat_orchestrator.process_user_query(
            question, role, user_context, chat_history
        )
        
        if not processing_result['success']:
            return jsonify({
                'error': 'Failed to process query',
                'details': processing_result.get('error')
            }), 500
        
        # Create the chat message record
        message = EnhancedDatabaseChatMessage(
            session_id=session_id,
            question=question,
            answer=processing_result['answer'],
            planner_output=processing_result.get('execution_plan'),
            sql_agent_queries={'sql_query': processing_result.get('sql_query')},
            validator_feedback=processing_result.get('validation_result'),
            qa_agent_feedback=processing_result.get('quality_assessment'),
            final_sql_query=processing_result.get('validated_sql'),
            query_results=processing_result.get('masked_results'),
            execution_time_ms=processing_result.get('execution_time_ms'),
            visualization_data=processing_result.get('visualization'),
            visualization_type=processing_result.get('visualization', {}).get('visualization_type'),
            total_processing_time_ms=processing_result.get('execution_metadata', {}).get('total_processing_time_ms')
        )
        
        db.session.add(message)
        
        # Update session activity
        session.last_activity_at = datetime.utcnow()
        session.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        # Update memory contexts based on this interaction
        memory_update_result = memory_manager.update_memory_from_interaction(message)
        
        logger.info(f"Processed message {message.id} in session {session_id}")
        
        # Format response
        response_data = {
            'success': True,
            'message': {
                'id': message.id,
                'question': message.question,
                'answer': message.answer,
                'sql_query': message.final_sql_query,
                'execution_time_ms': message.execution_time_ms,
                'visualization': message.visualization_data,
                'created_at': message.created_at.isoformat() if message.created_at else None
            },
            'processing_metadata': {
                'total_processing_time_ms': message.total_processing_time_ms,
                'steps_completed': len(processing_result.get('processing_steps', [])),
                'quality_score': processing_result.get('quality_assessment', {}).get('overall_score'),
                'memory_updates': memory_update_result.get('total_updates', 0)
            }
        }
        
        return jsonify(response_data), 201
        
    except Exception as e:
        logger.error(f"Error sending message: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@enhanced_database_chat_bp.route('/enhanced-chat/sessions', methods=['GET'])
@login_required
def list_chat_sessions():
    """List enhanced chat sessions for the user"""
    try:
        # Get query parameters
        role_id = request.args.get('role_id', type=int)
        active_only = request.args.get('active_only', 'true').lower() == 'true'
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build query
        query = EnhancedDatabaseChatSession.query.filter_by(user_id=request.user.id)
        
        if role_id:
            query = query.filter_by(database_role_id=role_id)
        
        if active_only:
            query = query.filter_by(is_active=True)
        
        # Order by most recent activity
        query = query.order_by(EnhancedDatabaseChatSession.last_activity_at.desc())
        
        # Apply pagination
        total_count = query.count()
        sessions = query.offset(offset).limit(limit).all()
        
        # Format response
        sessions_data = []
        for session in sessions:
            # Get message count
            message_count = EnhancedDatabaseChatMessage.query.filter_by(
                session_id=session.id
            ).count()
            
            # Get last message
            last_message = EnhancedDatabaseChatMessage.query.filter_by(
                session_id=session.id
            ).order_by(EnhancedDatabaseChatMessage.created_at.desc()).first()
            
            session_data = {
                'id': session.id,
                'role_id': session.database_role_id,
                'role_name': session.role.name,
                'session_name': session.session_name,
                'session_description': session.session_description,
                'is_active': session.is_active,
                'message_count': message_count,
                'last_message_preview': last_message.question[:100] + '...' if last_message and len(last_message.question) > 100 else last_message.question if last_message else None,
                'created_at': session.created_at.isoformat() if session.created_at else None,
                'last_activity_at': session.last_activity_at.isoformat() if session.last_activity_at else None
            }
            sessions_data.append(session_data)
        
        return jsonify({
            'success': True,
            'sessions': sessions_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing chat sessions: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@enhanced_database_chat_bp.route('/enhanced-chat/sessions/<int:session_id>/messages', methods=['GET'])
@login_required
def get_session_messages():
    """Get messages from a chat session"""
    try:
        session_id = request.view_args['session_id']
        
        # Check if session exists and user has access
        session = EnhancedDatabaseChatSession.query.get(session_id)
        if not session:
            return jsonify({'error': 'Chat session not found'}), 404
        
        if session.user_id != request.user.id and not request.user.is_admin:
            return jsonify({'error': 'Access denied to this chat session'}), 403
        
        # Get query parameters
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        include_metadata = request.args.get('include_metadata', 'false').lower() == 'true'
        
        # Get messages
        query = EnhancedDatabaseChatMessage.query.filter_by(session_id=session_id)
        query = query.order_by(EnhancedDatabaseChatMessage.created_at.desc())
        
        total_count = query.count()
        messages = query.offset(offset).limit(limit).all()
        
        # Format response
        messages_data = []
        for message in reversed(messages):  # Reverse to get chronological order
            message_data = {
                'id': message.id,
                'question': message.question,
                'answer': message.answer,
                'sql_query': message.final_sql_query,
                'execution_time_ms': message.execution_time_ms,
                'visualization': message.visualization_data,
                'visualization_type': message.visualization_type,
                'created_at': message.created_at.isoformat() if message.created_at else None
            }
            
            if include_metadata:
                message_data['metadata'] = {
                    'planner_output': message.planner_output,
                    'validator_feedback': message.validator_feedback,
                    'qa_feedback': message.qa_agent_feedback,
                    'total_processing_time_ms': message.total_processing_time_ms,
                    'query_results_count': len(message.query_results) if message.query_results else 0
                }
            
            messages_data.append(message_data)
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'session_name': session.session_name,
            'messages': messages_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting session messages: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@enhanced_database_chat_bp.route('/enhanced-chat/sessions/<int:session_id>/close', methods=['POST'])
@login_required
def close_chat_session():
    """Close an enhanced chat session"""
    try:
        session_id = request.view_args['session_id']
        
        session = EnhancedDatabaseChatSession.query.get(session_id)
        if not session:
            return jsonify({'error': 'Chat session not found'}), 404
        
        if session.user_id != request.user.id and not request.user.is_admin:
            return jsonify({'error': 'Access denied to this chat session'}), 403
        
        session.is_active = False
        session.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        logger.info(f"Closed chat session {session_id}")
        
        return jsonify({
            'success': True,
            'message': 'Chat session closed successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Error closing chat session: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

def _summarize_memory_context(context: dict) -> str:
    """Create a brief summary of memory context for API response"""
    try:
        if not context:
            return "No context available"
        
        summary_parts = []
        
        if 'user_info' in context:
            summary_parts.append("User preferences and history available")
        
        if 'database_info' in context:
            db_info = context['database_info']
            summary_parts.append(f"Database: {db_info.get('name', 'Unknown')}")
        
        if 'usage_patterns' in context:
            patterns = context['usage_patterns']
            if patterns.get('common_queries'):
                summary_parts.append(f"{len(patterns['common_queries'])} common query patterns")
        
        return "; ".join(summary_parts) if summary_parts else "Basic context available"
        
    except Exception as e:
        logger.error(f"Error summarizing memory context: {str(e)}")
        return "Context summary unavailable"
