"""
Database Role Management API Routes

This module provides REST API endpoints for managing database roles:
- Create and configure roles
- Manage role permissions and restrictions
- Assign users to roles
- Manage trainers for roles
"""

from flask import Blueprint, request, jsonify
from flask_cors import CORS
import logging
import traceback
from datetime import datetime
from sqlalchemy import and_, or_

from models.models import (
    db, DatabaseRole, ExternalDatabase, User, DatabaseSchema, DatabaseTable,
    user_database_roles, role_trainers, DatabaseSemanticSummary
)
from utils.decorators import login_required, require_scope
from utils.request_utils import get_request_json

logger = logging.getLogger(__name__)

# Create blueprint
database_role_bp = Blueprint('database_role', __name__)
CORS(database_role_bp)

@database_role_bp.route('/database-roles', methods=['POST'])
@login_required
def create_database_role():
    """
    Create a new database role
    
    Request Body:
    {
        "name": "Sales_India",
        "description": "Sales data access for India region",
        "external_database_id": 1,
        "where_conditions": "WHERE country = 'India'",
        "allowed_tables": ["sales", "customers"],
        "restricted_tables": ["employee_salaries"],
        "column_masking_rules": {
            "customers": {
                "email": "mask",
                "phone": "mask"
            }
        }
    }
    """
    try:
        data = get_request_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['name', 'external_database_id']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if database exists and user has access
        external_db = ExternalDatabase.query.get(data['external_database_id'])
        if not external_db:
            return jsonify({'error': 'Database not found'}), 404
        
        if not request.user.is_admin and external_db.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions to create roles for this database'}), 403
        
        # Check if role name is unique for this database
        existing_role = DatabaseRole.query.filter_by(
            name=data['name'],
            external_database_id=data['external_database_id']
        ).first()
        
        if existing_role:
            return jsonify({'error': 'Role name already exists for this database'}), 409
        
        # Validate table restrictions
        validation_result = _validate_table_restrictions(
            data['external_database_id'],
            data.get('allowed_tables'),
            data.get('restricted_tables')
        )
        
        if not validation_result['valid']:
            return jsonify({'error': validation_result['error']}), 400
        
        # Create the role
        role = DatabaseRole(
            name=data['name'],
            description=data.get('description', ''),
            external_database_id=data['external_database_id'],
            where_conditions=data.get('where_conditions'),
            allowed_tables=data.get('allowed_tables'),
            restricted_tables=data.get('restricted_tables'),
            column_masking_rules=data.get('column_masking_rules'),
            created_by=request.user.id
        )
        
        db.session.add(role)
        db.session.commit()
        
        logger.info(f"Created database role '{role.name}' for database '{external_db.name}' by user {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Database role created successfully',
            'role': _format_role_response(role)
        }), 201
        
    except Exception as e:
        logger.error(f"Error creating database role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_role_bp.route('/database-roles/<int:role_id>', methods=['GET'])
@login_required
def get_database_role(role_id):
    """Get detailed information about a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check if user has access
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Get additional information
        role_data = _format_role_response(role)
        
        # Add assigned users count
        assigned_users_count = db.session.query(user_database_roles).filter_by(
            database_role_id=role_id,
            is_active=True
        ).count()
        role_data['assigned_users_count'] = assigned_users_count
        
        # Add trainers count
        trainers_count = db.session.query(role_trainers).filter_by(
            database_role_id=role_id,
            is_active=True
        ).count()
        role_data['trainers_count'] = trainers_count
        
        # Add semantic summaries count
        summaries_count = DatabaseSemanticSummary.query.filter_by(
            database_role_id=role_id,
            is_active=True
        ).count()
        role_data['semantic_summaries_count'] = summaries_count
        
        return jsonify({
            'success': True,
            'role': role_data
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting database role: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_role_bp.route('/database-roles/<int:role_id>', methods=['PUT'])
@login_required
def update_database_role(role_id):
    """Update a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check if user has access
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update allowed fields
        updatable_fields = [
            'description', 'where_conditions', 'allowed_tables', 
            'restricted_tables', 'column_masking_rules'
        ]
        
        for field in updatable_fields:
            if field in data:
                setattr(role, field, data[field])
        
        # Validate table restrictions if they were updated
        if 'allowed_tables' in data or 'restricted_tables' in data:
            validation_result = _validate_table_restrictions(
                role.external_database_id,
                data.get('allowed_tables', role.allowed_tables),
                data.get('restricted_tables', role.restricted_tables)
            )
            
            if not validation_result['valid']:
                return jsonify({'error': validation_result['error']}), 400
        
        # If role is published and has users, mark for re-training
        if role.is_published and role.assigned_users:
            role.training_status = 'needs_update'
        
        role.updated_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f"Updated database role '{role.name}' by user {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Database role updated successfully',
            'role': _format_role_response(role)
        }), 200
        
    except Exception as e:
        logger.error(f"Error updating database role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_role_bp.route('/database-roles/<int:role_id>', methods=['DELETE'])
@login_required
def delete_database_role(role_id):
    """Delete a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check if user has access
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Check if role has assigned users
        assigned_users_count = db.session.query(user_database_roles).filter_by(
            database_role_id=role_id,
            is_active=True
        ).count()
        
        if assigned_users_count > 0:
            return jsonify({
                'error': 'Cannot delete role with assigned users. Remove all user assignments first.'
            }), 409
        
        role_name = role.name
        db.session.delete(role)
        db.session.commit()
        
        logger.info(f"Deleted database role '{role_name}' by user {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Database role deleted successfully'
        }), 200
        
    except Exception as e:
        logger.error(f"Error deleting database role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_role_bp.route('/database-roles', methods=['GET'])
@login_required
def list_database_roles():
    """List database roles with filtering options"""
    try:
        # Get query parameters
        database_id = request.args.get('database_id', type=int)
        status = request.args.get('status')  # active, published, training
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build query
        query = DatabaseRole.query.join(ExternalDatabase)
        
        # Filter by user access
        if not request.user.is_admin:
            query = query.filter(ExternalDatabase.created_by == request.user.id)
        
        # Apply filters
        if database_id:
            query = query.filter(DatabaseRole.external_database_id == database_id)
        
        if status:
            if status == 'active':
                query = query.filter(DatabaseRole.is_active == True)
            elif status == 'published':
                query = query.filter(DatabaseRole.is_published == True)
            elif status == 'training':
                query = query.filter(DatabaseRole.training_status.in_(['in_progress', 'needs_update']))
        
        # Order by most recent first
        query = query.order_by(DatabaseRole.created_at.desc())
        
        # Apply pagination
        total_count = query.count()
        roles = query.offset(offset).limit(limit).all()
        
        # Format response
        roles_data = []
        for role in roles:
            role_data = _format_role_response(role)
            
            # Add summary statistics
            assigned_users_count = db.session.query(user_database_roles).filter_by(
                database_role_id=role.id,
                is_active=True
            ).count()
            role_data['assigned_users_count'] = assigned_users_count
            
            trainers_count = db.session.query(role_trainers).filter_by(
                database_role_id=role.id,
                is_active=True
            ).count()
            role_data['trainers_count'] = trainers_count
            
            roles_data.append(role_data)
        
        return jsonify({
            'success': True,
            'roles': roles_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing database roles: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_role_bp.route('/database-roles/<int:role_id>/publish', methods=['POST'])
@login_required
def publish_database_role(role_id):
    """Publish a database role for production use"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check if user has access
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Check if role is ready for publishing
        if role.training_status != 'completed':
            return jsonify({
                'error': 'Role must complete training before it can be published'
            }), 400
        
        # Check if role has semantic summaries
        summaries_count = DatabaseSemanticSummary.query.filter_by(
            database_role_id=role_id,
            is_active=True
        ).count()
        
        if summaries_count == 0:
            return jsonify({
                'error': 'Role must have semantic summaries before it can be published'
            }), 400
        
        role.is_published = True
        role.updated_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f"Published database role '{role.name}' by user {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Database role published successfully',
            'role': _format_role_response(role)
        }), 200
        
    except Exception as e:
        logger.error(f"Error publishing database role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_role_bp.route('/database-roles/<int:role_id>/unpublish', methods=['POST'])
@login_required
def unpublish_database_role(role_id):
    """Unpublish a database role"""
    try:
        role = DatabaseRole.query.get(role_id)
        if not role:
            return jsonify({'error': 'Role not found'}), 404
        
        # Check if user has access
        if not request.user.is_admin and role.database.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        role.is_published = False
        role.updated_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f"Unpublished database role '{role.name}' by user {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Database role unpublished successfully',
            'role': _format_role_response(role)
        }), 200
        
    except Exception as e:
        logger.error(f"Error unpublishing database role: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

def _validate_table_restrictions(database_id: int, allowed_tables: list, restricted_tables: list) -> dict:
    """Validate table restrictions against actual database schema"""
    try:
        # Get database schema
        schema = DatabaseSchema.query.filter_by(external_database_id=database_id).first()
        if not schema:
            return {
                'valid': False,
                'error': 'Database schema not found. Please run schema analysis first.'
            }
        
        # Get actual table names
        actual_tables = set(schema.schema_data.get('tables', {}).keys())
        
        # Validate allowed tables
        if allowed_tables:
            invalid_allowed = set(allowed_tables) - actual_tables
            if invalid_allowed:
                return {
                    'valid': False,
                    'error': f'Invalid allowed tables: {", ".join(invalid_allowed)}'
                }
        
        # Validate restricted tables
        if restricted_tables:
            invalid_restricted = set(restricted_tables) - actual_tables
            if invalid_restricted:
                return {
                    'valid': False,
                    'error': f'Invalid restricted tables: {", ".join(invalid_restricted)}'
                }
        
        # Check for conflicts between allowed and restricted
        if allowed_tables and restricted_tables:
            conflicts = set(allowed_tables) & set(restricted_tables)
            if conflicts:
                return {
                    'valid': False,
                    'error': f'Tables cannot be both allowed and restricted: {", ".join(conflicts)}'
                }
        
        return {'valid': True}
        
    except Exception as e:
        logger.error(f"Error validating table restrictions: {str(e)}")
        return {
            'valid': False,
            'error': 'Error validating table restrictions'
        }

def _format_role_response(role: DatabaseRole) -> dict:
    """Format role object for API response"""
    return {
        'id': role.id,
        'name': role.name,
        'description': role.description,
        'database_id': role.external_database_id,
        'database_name': role.database.name,
        'database_type': role.database.db_type,
        'where_conditions': role.where_conditions,
        'allowed_tables': role.allowed_tables,
        'restricted_tables': role.restricted_tables,
        'column_masking_rules': role.column_masking_rules,
        'is_active': role.is_active,
        'is_published': role.is_published,
        'training_status': role.training_status,
        'created_by': role.creator.user_name if role.creator else 'Unknown',
        'created_at': role.created_at.isoformat() if role.created_at else None,
        'updated_at': role.updated_at.isoformat() if role.updated_at else None
    }
