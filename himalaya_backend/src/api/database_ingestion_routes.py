"""
Database Ingestion API Routes

This module provides REST API endpoints for database ingestion operations:
- Start ingestion jobs
- Monitor ingestion progress
- Manage ingestion results
"""

from flask import Blueprint, request, jsonify
from flask_cors import CORS
import logging
import traceback
from datetime import datetime

from models.models import (
    db, ExternalDatabase, DatabaseIngestionJob, DatabaseSchema, 
    DatabaseEDA, DatabaseSemanticSummary, User
)
from utils.decorators import login_required, require_scope
from utils.database_ingestion_orchestrator import DatabaseIngestionOrchestrator
from utils.request_utils import get_request_json

logger = logging.getLogger(__name__)

# Create blueprint
database_ingestion_bp = Blueprint('database_ingestion', __name__)
CORS(database_ingestion_bp)

# Initialize orchestrator
orchestrator = DatabaseIngestionOrchestrator()

@database_ingestion_bp.route('/database-ingestion/start', methods=['POST'])
@login_required
def start_database_ingestion():
    """
    Start a database ingestion job
    
    Request Body:
    {
        "external_database_id": 1,
        "job_type": "full_ingestion"  // full_ingestion, schema_only, eda_only
    }
    """
    try:
        data = get_request_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        external_database_id = data.get('external_database_id')
        job_type = data.get('job_type', 'full_ingestion')
        
        if not external_database_id:
            return jsonify({'error': 'external_database_id is required'}), 400
        
        # Validate job type
        valid_job_types = ['full_ingestion', 'schema_only', 'eda_only']
        if job_type not in valid_job_types:
            return jsonify({
                'error': f'Invalid job_type. Must be one of: {", ".join(valid_job_types)}'
            }), 400
        
        # Check if database exists and user has access
        external_db = ExternalDatabase.query.get(external_database_id)
        if not external_db:
            return jsonify({'error': 'Database not found'}), 404
        
        # Check if user has admin access to this database
        if not request.user.is_admin and external_db.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Check if there's already a running job for this database
        existing_job = DatabaseIngestionJob.query.filter_by(
            external_database_id=external_database_id,
            status='running'
        ).first()
        
        if existing_job:
            return jsonify({
                'error': 'An ingestion job is already running for this database',
                'existing_job_id': existing_job.id
            }), 409
        
        # Start the ingestion job
        result = orchestrator.start_full_ingestion(
            external_database_id=external_database_id,
            user_id=request.user.id,
            job_type=job_type
        )
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Database ingestion started successfully',
                'job_id': result['job_id'],
                'job_type': job_type,
                'database_name': external_db.name
            }), 200
        else:
            return jsonify({
                'error': result.get('error', 'Failed to start ingestion'),
                'job_id': result.get('job_id')
            }), 500
            
    except Exception as e:
        logger.error(f"Error starting database ingestion: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({'error': 'Internal server error'}), 500

@database_ingestion_bp.route('/database-ingestion/status/<int:job_id>', methods=['GET'])
@login_required
def get_ingestion_status(job_id):
    """Get the status of a database ingestion job"""
    try:
        # Get job details
        job = DatabaseIngestionJob.query.get(job_id)
        if not job:
            return jsonify({'error': 'Job not found'}), 404
        
        # Check if user has access to this job
        external_db = ExternalDatabase.query.get(job.external_database_id)
        if not request.user.is_admin and external_db.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Get detailed status
        status_result = orchestrator.get_ingestion_status(job_id)
        
        if status_result['success']:
            # Add additional context
            status_result.update({
                'database_name': external_db.name,
                'database_type': external_db.db_type,
                'started_by': job.starter.user_name if job.starter else 'Unknown'
            })
            
            return jsonify(status_result), 200
        else:
            return jsonify({'error': status_result.get('error', 'Failed to get status')}), 500
            
    except Exception as e:
        logger.error(f"Error getting ingestion status: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_ingestion_bp.route('/database-ingestion/jobs', methods=['GET'])
@login_required
def list_ingestion_jobs():
    """List ingestion jobs for databases the user has access to"""
    try:
        # Get query parameters
        status_filter = request.args.get('status')  # pending, running, completed, failed
        database_id = request.args.get('database_id', type=int)
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build query
        query = DatabaseIngestionJob.query.join(ExternalDatabase)
        
        # Filter by user access
        if not request.user.is_admin:
            query = query.filter(ExternalDatabase.created_by == request.user.id)
        
        # Apply filters
        if status_filter:
            query = query.filter(DatabaseIngestionJob.status == status_filter)
        
        if database_id:
            query = query.filter(DatabaseIngestionJob.external_database_id == database_id)
        
        # Order by most recent first
        query = query.order_by(DatabaseIngestionJob.started_at.desc())
        
        # Apply pagination
        total_count = query.count()
        jobs = query.offset(offset).limit(limit).all()
        
        # Format response
        jobs_data = []
        for job in jobs:
            job_data = {
                'job_id': job.id,
                'database_id': job.external_database_id,
                'database_name': job.database.name,
                'database_type': job.database.db_type,
                'job_type': job.job_type,
                'status': job.status,
                'current_step': job.current_step,
                'total_tables': job.total_tables,
                'processed_tables': job.processed_tables,
                'progress_percentage': (job.processed_tables / job.total_tables * 100) if job.total_tables else 0,
                'schema_completed': job.schema_analysis_completed,
                'eda_completed': job.eda_completed,
                'summaries_completed': job.summary_generation_completed,
                'started_at': job.started_at.isoformat() if job.started_at else None,
                'completed_at': job.completed_at.isoformat() if job.completed_at else None,
                'started_by': job.starter.user_name if job.starter else 'Unknown',
                'error_message': job.error_message
            }
            jobs_data.append(job_data)
        
        return jsonify({
            'success': True,
            'jobs': jobs_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing ingestion jobs: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_ingestion_bp.route('/database-ingestion/retry/<int:job_id>', methods=['POST'])
@login_required
def retry_ingestion_job(job_id):
    """Retry a failed ingestion job"""
    try:
        # Get job details
        job = DatabaseIngestionJob.query.get(job_id)
        if not job:
            return jsonify({'error': 'Job not found'}), 404
        
        # Check if user has access to this job
        external_db = ExternalDatabase.query.get(job.external_database_id)
        if not request.user.is_admin and external_db.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Retry the job
        result = orchestrator.retry_failed_job(job_id, request.user.id)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': 'Job retry started successfully',
                'job_id': job_id
            }), 200
        else:
            return jsonify({'error': result.get('error', 'Failed to retry job')}), 500
            
    except Exception as e:
        logger.error(f"Error retrying ingestion job: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_ingestion_bp.route('/database-ingestion/results/<int:database_id>', methods=['GET'])
@login_required
def get_ingestion_results(database_id):
    """Get ingestion results for a database"""
    try:
        # Check if database exists and user has access
        external_db = ExternalDatabase.query.get(database_id)
        if not external_db:
            return jsonify({'error': 'Database not found'}), 404
        
        if not request.user.is_admin and external_db.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Get schema information
        schema = DatabaseSchema.query.filter_by(external_database_id=database_id).first()
        schema_data = None
        if schema:
            schema_data = {
                'id': schema.id,
                'table_count': schema.table_count,
                'column_count': schema.column_count,
                'analysis_status': schema.analysis_status,
                'last_analyzed_at': schema.last_analyzed_at.isoformat() if schema.last_analyzed_at else None,
                'tables': []
            }
            
            # Get table information
            tables = schema.tables
            for table in tables:
                table_data = {
                    'id': table.id,
                    'name': table.table_name,
                    'type': table.table_type,
                    'row_count': table.row_count,
                    'column_count': table.column_count
                }
                schema_data['tables'].append(table_data)
        
        # Get EDA results
        eda_results = DatabaseEDA.query.filter_by(external_database_id=database_id).all()
        eda_data = []
        for eda in eda_results:
            eda_item = {
                'id': eda.id,
                'eda_type': eda.eda_type,
                'table_name': eda.table.table_name if eda.table else None,
                'analysis_status': eda.analysis_status,
                'created_at': eda.created_at.isoformat() if eda.created_at else None
            }
            eda_data.append(eda_item)
        
        # Get semantic summaries
        summaries = DatabaseSemanticSummary.query.filter_by(external_database_id=database_id).all()
        summaries_data = []
        for summary in summaries:
            summary_item = {
                'id': summary.id,
                'summary_type': summary.summary_type,
                'target_name': summary.target_name,
                'token_count': summary.token_count,
                'version': summary.version,
                'is_active': summary.is_active,
                'created_at': summary.created_at.isoformat() if summary.created_at else None,
                'updated_at': summary.updated_at.isoformat() if summary.updated_at else None
            }
            summaries_data.append(summary_item)
        
        # Get latest ingestion job
        latest_job = DatabaseIngestionJob.query.filter_by(
            external_database_id=database_id
        ).order_by(DatabaseIngestionJob.started_at.desc()).first()
        
        job_data = None
        if latest_job:
            job_data = {
                'job_id': latest_job.id,
                'job_type': latest_job.job_type,
                'status': latest_job.status,
                'started_at': latest_job.started_at.isoformat() if latest_job.started_at else None,
                'completed_at': latest_job.completed_at.isoformat() if latest_job.completed_at else None,
                'error_message': latest_job.error_message
            }
        
        return jsonify({
            'success': True,
            'database_id': database_id,
            'database_name': external_db.name,
            'schema': schema_data,
            'eda_results': eda_data,
            'semantic_summaries': summaries_data,
            'latest_job': job_data
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting ingestion results: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_ingestion_bp.route('/database-ingestion/summary/<int:summary_id>', methods=['GET'])
@login_required
def get_semantic_summary(summary_id):
    """Get detailed semantic summary"""
    try:
        summary = DatabaseSemanticSummary.query.get(summary_id)
        if not summary:
            return jsonify({'error': 'Summary not found'}), 404
        
        # Check if user has access
        external_db = summary.database
        if not request.user.is_admin and external_db.created_by != request.user.id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        summary_data = {
            'id': summary.id,
            'database_id': summary.external_database_id,
            'database_name': external_db.name,
            'table_id': summary.table_id,
            'table_name': summary.table.table_name if summary.table else None,
            'summary_type': summary.summary_type,
            'target_name': summary.target_name,
            'llm_generated_summary': summary.llm_generated_summary,
            'human_edited_summary': summary.human_edited_summary,
            'current_summary': summary.current_summary,
            'business_description': summary.business_description,
            'usage_patterns': summary.usage_patterns,
            'data_lineage': summary.data_lineage,
            'quality_notes': summary.quality_notes,
            'token_count': summary.token_count,
            'version': summary.version,
            'is_active': summary.is_active,
            'created_at': summary.created_at.isoformat() if summary.created_at else None,
            'updated_at': summary.updated_at.isoformat() if summary.updated_at else None,
            'created_by': summary.creator.user_name if summary.creator else 'System',
            'last_edited_by': summary.editor.user_name if summary.editor else None
        }
        
        return jsonify({
            'success': True,
            'summary': summary_data
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting semantic summary: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500
