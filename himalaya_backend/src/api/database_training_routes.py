"""
Database Training Interface API Routes

This module provides REST API endpoints for the human training system:
- Start and manage training sessions
- Provide Q&A feedback
- Edit semantic summaries
- Track training progress
"""

from flask import Blueprint, request, jsonify
from flask_cors import CORS
import logging
import traceback
from datetime import datetime
from sqlalchemy import and_, or_

from models.models import (
    db, DatabaseRole, TrainingSession, TrainingInteraction, 
    DatabaseSemanticSummary, User, role_trainers
)
from utils.decorators import login_required, require_scope
from utils.request_utils import get_request_json

logger = logging.getLogger(__name__)

# Create blueprint
database_training_bp = Blueprint('database_training', __name__)
CORS(database_training_bp)

@database_training_bp.route('/training/sessions', methods=['POST'])
@login_required
def start_training_session():
    """
    Start a new training session for a database role
    
    Request Body:
    {
        "database_role_id": 1,
        "session_name": "Sales Role Training - Week 1",
        "session_description": "Training session to improve sales data queries"
    }
    """
    try:
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        database_role_id = data.get('database_role_id')
        if not database_role_id:
            return jsonify({'error': 'database_role_id is required'}), 400
        
        # Check if role exists
        role = DatabaseRole.query.get(database_role_id)
        if not role:
            return jsonify({'error': 'Database role not found'}), 404
        
        # Check if user is a trainer for this role
        is_trainer = db.session.query(role_trainers).filter_by(
            user_id=request.user.id,
            database_role_id=database_role_id,
            is_active=True
        ).first()
        
        if not is_trainer and not request.user.is_admin:
            return jsonify({'error': 'You are not authorized to train this role'}), 403
        
        # Check if there's already an active training session
        active_session = TrainingSession.query.filter_by(
            database_role_id=database_role_id,
            trainer_id=request.user.id,
            status='active'
        ).first()
        
        if active_session:
            return jsonify({
                'error': 'You already have an active training session for this role',
                'active_session_id': active_session.id
            }), 409
        
        # Create new training session
        session = TrainingSession(
            database_role_id=database_role_id,
            trainer_id=request.user.id,
            session_name=data.get('session_name'),
            session_description=data.get('session_description'),
            status='active'
        )
        
        db.session.add(session)
        db.session.commit()
        
        logger.info(f"Started training session {session.id} for role {role.name} by trainer {request.user.user_name}")
        
        return jsonify({
            'success': True,
            'message': 'Training session started successfully',
            'session': {
                'id': session.id,
                'role_id': database_role_id,
                'role_name': role.name,
                'session_name': session.session_name,
                'session_description': session.session_description,
                'status': session.status,
                'started_at': session.started_at.isoformat() if session.started_at else None
            }
        }), 201
        
    except Exception as e:
        logger.error(f"Error starting training session: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_training_bp.route('/training/sessions/<int:session_id>/interactions', methods=['POST'])
@login_required
def add_training_interaction():
    """
    Add a Q&A interaction to a training session
    
    Request Body:
    {
        "question": "Show me sales data for Q1 2024",
        "system_answer": "Here is the sales data...",
        "sql_query": "SELECT * FROM sales WHERE date >= '2024-01-01'",
        "query_results": [...],
        "feedback_rating": 4,
        "feedback_text": "Good answer but could include more details",
        "suggested_improvements": "Add trend analysis"
    }
    """
    try:
        session_id = request.view_args['session_id']
        
        # Check if session exists and user has access
        session = TrainingSession.query.get(session_id)
        if not session:
            return jsonify({'error': 'Training session not found'}), 404
        
        if session.trainer_id != request.user.id and not request.user.is_admin:
            return jsonify({'error': 'Access denied to this training session'}), 403
        
        if session.status != 'active':
            return jsonify({'error': 'Training session is not active'}), 400
        
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['question', 'system_answer']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400
        
        # Create training interaction
        interaction = TrainingInteraction(
            training_session_id=session_id,
            question=data['question'],
            system_answer=data['system_answer'],
            sql_query=data.get('sql_query'),
            query_results=data.get('query_results'),
            feedback_rating=data.get('feedback_rating'),
            feedback_text=data.get('feedback_text'),
            suggested_improvements=data.get('suggested_improvements')
        )
        
        # If feedback is provided, set feedback timestamp
        if data.get('feedback_rating') or data.get('feedback_text'):
            interaction.feedback_provided_at = datetime.utcnow()
        
        db.session.add(interaction)
        db.session.commit()
        
        logger.info(f"Added training interaction to session {session_id}")
        
        return jsonify({
            'success': True,
            'message': 'Training interaction added successfully',
            'interaction': {
                'id': interaction.id,
                'question': interaction.question,
                'feedback_rating': interaction.feedback_rating,
                'created_at': interaction.created_at.isoformat() if interaction.created_at else None
            }
        }), 201
        
    except Exception as e:
        logger.error(f"Error adding training interaction: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_training_bp.route('/training/sessions/<int:session_id>/complete', methods=['POST'])
@login_required
def complete_training_session():
    """Complete a training session"""
    try:
        session_id = request.view_args['session_id']
        
        session = TrainingSession.query.get(session_id)
        if not session:
            return jsonify({'error': 'Training session not found'}), 404
        
        if session.trainer_id != request.user.id and not request.user.is_admin:
            return jsonify({'error': 'Access denied to this training session'}), 403
        
        if session.status != 'active':
            return jsonify({'error': 'Training session is not active'}), 400
        
        # Update session status
        session.status = 'completed'
        session.completed_at = datetime.utcnow()
        
        # Update role training status if this was the first completed session
        role = session.role
        if role.training_status == 'not_started':
            role.training_status = 'in_progress'
        
        # Check if role has enough training to be marked as completed
        completed_sessions = TrainingSession.query.filter_by(
            database_role_id=role.id,
            status='completed'
        ).count()
        
        if completed_sessions >= 3:  # Configurable threshold
            role.training_status = 'completed'
        
        db.session.commit()
        
        logger.info(f"Completed training session {session_id} for role {role.name}")
        
        return jsonify({
            'success': True,
            'message': 'Training session completed successfully',
            'session': {
                'id': session.id,
                'status': session.status,
                'completed_at': session.completed_at.isoformat() if session.completed_at else None
            },
            'role_training_status': role.training_status
        }), 200
        
    except Exception as e:
        logger.error(f"Error completing training session: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500

@database_training_bp.route('/training/sessions', methods=['GET'])
@login_required
def list_training_sessions():
    """List training sessions for roles the user can train"""
    try:
        # Get query parameters
        role_id = request.args.get('role_id', type=int)
        status = request.args.get('status')  # active, completed, paused
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build query
        query = TrainingSession.query.join(DatabaseRole)
        
        # Filter by user access (trainers only see their sessions, admins see all)
        if not request.user.is_admin:
            query = query.filter(TrainingSession.trainer_id == request.user.id)
        
        # Apply filters
        if role_id:
            query = query.filter(TrainingSession.database_role_id == role_id)
        
        if status:
            query = query.filter(TrainingSession.status == status)
        
        # Order by most recent first
        query = query.order_by(TrainingSession.started_at.desc())
        
        # Apply pagination
        total_count = query.count()
        sessions = query.offset(offset).limit(limit).all()
        
        # Format response
        sessions_data = []
        for session in sessions:
            # Get interaction count
            interaction_count = TrainingInteraction.query.filter_by(
                training_session_id=session.id
            ).count()
            
            session_data = {
                'id': session.id,
                'role_id': session.database_role_id,
                'role_name': session.role.name,
                'session_name': session.session_name,
                'session_description': session.session_description,
                'status': session.status,
                'interaction_count': interaction_count,
                'trainer_name': session.trainer.user_name if session.trainer else 'Unknown',
                'started_at': session.started_at.isoformat() if session.started_at else None,
                'completed_at': session.completed_at.isoformat() if session.completed_at else None
            }
            sessions_data.append(session_data)
        
        return jsonify({
            'success': True,
            'sessions': sessions_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing training sessions: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_training_bp.route('/training/sessions/<int:session_id>/interactions', methods=['GET'])
@login_required
def list_training_interactions():
    """List interactions for a training session"""
    try:
        session_id = request.view_args['session_id']
        
        session = TrainingSession.query.get(session_id)
        if not session:
            return jsonify({'error': 'Training session not found'}), 404
        
        if session.trainer_id != request.user.id and not request.user.is_admin:
            return jsonify({'error': 'Access denied to this training session'}), 403
        
        # Get query parameters
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Get interactions
        query = TrainingInteraction.query.filter_by(training_session_id=session_id)
        query = query.order_by(TrainingInteraction.created_at.desc())
        
        total_count = query.count()
        interactions = query.offset(offset).limit(limit).all()
        
        # Format response
        interactions_data = []
        for interaction in interactions:
            interaction_data = {
                'id': interaction.id,
                'question': interaction.question,
                'system_answer': interaction.system_answer,
                'sql_query': interaction.sql_query,
                'feedback_rating': interaction.feedback_rating,
                'feedback_text': interaction.feedback_text,
                'suggested_improvements': interaction.suggested_improvements,
                'applied_corrections': interaction.applied_corrections,
                'created_at': interaction.created_at.isoformat() if interaction.created_at else None,
                'feedback_provided_at': interaction.feedback_provided_at.isoformat() if interaction.feedback_provided_at else None
            }
            interactions_data.append(interaction_data)
        
        return jsonify({
            'success': True,
            'session_id': session_id,
            'interactions': interactions_data,
            'pagination': {
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total_count
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing training interactions: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500

@database_training_bp.route('/training/interactions/<int:interaction_id>/feedback', methods=['PUT'])
@login_required
def update_interaction_feedback():
    """Update feedback for a training interaction"""
    try:
        interaction_id = request.view_args['interaction_id']
        
        interaction = TrainingInteraction.query.get(interaction_id)
        if not interaction:
            return jsonify({'error': 'Training interaction not found'}), 404
        
        # Check access through session
        session = interaction.session
        if session.trainer_id != request.user.id and not request.user.is_admin:
            return jsonify({'error': 'Access denied to this training interaction'}), 403
        
        data = get_request_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update feedback fields
        if 'feedback_rating' in data:
            interaction.feedback_rating = data['feedback_rating']
        
        if 'feedback_text' in data:
            interaction.feedback_text = data['feedback_text']
        
        if 'suggested_improvements' in data:
            interaction.suggested_improvements = data['suggested_improvements']
        
        interaction.feedback_provided_at = datetime.utcnow()
        
        db.session.commit()
        
        logger.info(f"Updated feedback for training interaction {interaction_id}")
        
        return jsonify({
            'success': True,
            'message': 'Feedback updated successfully',
            'interaction': {
                'id': interaction.id,
                'feedback_rating': interaction.feedback_rating,
                'feedback_text': interaction.feedback_text,
                'suggested_improvements': interaction.suggested_improvements,
                'feedback_provided_at': interaction.feedback_provided_at.isoformat() if interaction.feedback_provided_at else None
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error updating interaction feedback: {str(e)}")
        logger.error(traceback.format_exc())
        db.session.rollback()
        return jsonify({'error': 'Internal server error'}), 500
