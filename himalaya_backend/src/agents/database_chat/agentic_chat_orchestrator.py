"""
Agentic Database Chat Orchestrator

This orchestrator coordinates all agents to provide complete query processing:
- Planner Agent: Analyzes queries and creates execution plans
- SQL Agent: Generates SQL queries
- Validator Agent: Ensures security and compliance
- Answer Maker Agent: Creates natural language responses
- QA Agent: Validates response quality
- Graph Agent: Creates visualizations
"""

import logging
import traceback
import time
from typing import Dict, List, Any, Optional
from datetime import datetime

from agents.database_chat.planner_agent import DatabaseChatPlannerAgent
from agents.database_chat.sql_agent import DatabaseChatSQLAgent
from agents.database_chat.validator_agent import DatabaseChatValidatorAgent
from agents.database_chat.answer_maker_agent import DatabaseChatAnswerMakerAgent
from agents.database_chat.qa_agent import DatabaseChatQAAgent
from agents.database_chat.graph_agent import DatabaseChatGraphAgent
from utils.database_role_schema_generator import DatabaseRoleSchemaGenerator
from utils.database_column_masking_engine import DatabaseColumnMaskingEngine
from models.models import DatabaseRole, DatabaseSemanticSummary

logger = logging.getLogger(__name__)

class AgenticDatabaseChatOrchestrator:
    """
    Orchestrator that coordinates all agents for complete query processing
    """
    
    def __init__(self):
        self.planner_agent = DatabaseChatPlannerAgent()
        self.sql_agent = DatabaseChatSQLAgent()
        self.validator_agent = DatabaseChatValidatorAgent()
        self.answer_maker_agent = DatabaseChatAnswerMakerAgent()
        self.qa_agent = DatabaseChatQAAgent()
        self.graph_agent = DatabaseChatGraphAgent()
        self.schema_generator = DatabaseRoleSchemaGenerator()
        self.masking_engine = DatabaseColumnMaskingEngine()
    
    def process_user_query(self, user_query: str, role: DatabaseRole, 
                          user_context: Dict[str, Any],
                          chat_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user query through the complete agentic pipeline
        
        Args:
            user_query: The user's natural language query
            role: DatabaseRole object with permissions and restrictions
            user_context: User and session context
            chat_history: Previous messages in the conversation
            
        Returns:
            Dictionary containing the complete response
        """
        try:
            start_time = time.time()
            logger.info(f"🚀 Starting agentic processing for query: {user_query[:100]}...")
            
            # Initialize response structure
            response = {
                'success': False,
                'user_query': user_query,
                'processing_steps': [],
                'execution_metadata': {
                    'started_at': datetime.utcnow().isoformat(),
                    'role_id': role.id,
                    'role_name': role.name
                }
            }
            
            # Step 1: Generate role schema and context
            logger.info("📋 Step 1: Preparing role context...")
            context_result = self._prepare_role_context(role)
            if not context_result['success']:
                return self._create_error_response(response, "Failed to prepare role context", context_result.get('error'))
            
            role_schema = context_result['role_schema']
            semantic_summaries = context_result['semantic_summaries']
            role_restrictions = context_result['role_restrictions']
            
            response['processing_steps'].append({
                'step': 1,
                'agent': 'context_preparation',
                'status': 'completed',
                'duration_ms': int((time.time() - start_time) * 1000)
            })
            
            # Step 2: Create execution plan
            step_start = time.time()
            logger.info("🤖 Step 2: Creating execution plan...")
            plan_result = self.planner_agent.create_execution_plan(
                user_query, role_schema, semantic_summaries, chat_history
            )
            
            if not plan_result['success']:
                return self._create_error_response(response, "Failed to create execution plan", plan_result.get('error'))
            
            execution_plan = plan_result['plan']
            response['execution_plan'] = execution_plan
            response['processing_steps'].append({
                'step': 2,
                'agent': 'planner',
                'status': 'completed',
                'duration_ms': int((time.time() - step_start) * 1000)
            })
            
            # Step 3: Generate SQL query
            step_start = time.time()
            logger.info("🔧 Step 3: Generating SQL query...")
            sql_result = self.sql_agent.generate_sql(
                user_query, execution_plan, role_schema, semantic_summaries, role_restrictions
            )
            
            if not sql_result['success']:
                return self._create_error_response(response, "Failed to generate SQL", sql_result.get('error'))
            
            sql_query = sql_result['sql_query']
            response['sql_query'] = sql_query
            response['sql_metadata'] = sql_result.get('metadata', {})
            response['processing_steps'].append({
                'step': 3,
                'agent': 'sql_generator',
                'status': 'completed',
                'duration_ms': int((time.time() - step_start) * 1000)
            })
            
            # Step 4: Validate SQL query
            step_start = time.time()
            logger.info("🔍 Step 4: Validating SQL query...")
            validation_result = self.validator_agent.validate_sql_query(
                sql_query, role_schema, role_restrictions, user_context
            )
            
            if not validation_result['is_valid']:
                return self._create_error_response(
                    response, 
                    "SQL validation failed", 
                    "; ".join(validation_result.get('errors', []))
                )
            
            validated_sql = validation_result['validated_sql']
            response['validated_sql'] = validated_sql
            response['validation_result'] = validation_result
            response['processing_steps'].append({
                'step': 4,
                'agent': 'validator',
                'status': 'completed',
                'duration_ms': int((time.time() - step_start) * 1000)
            })
            
            # Step 5: Execute SQL query (simulated - would connect to actual database)
            step_start = time.time()
            logger.info("⚡ Step 5: Executing SQL query...")
            execution_result = self._execute_sql_query(validated_sql, role, user_context)
            
            if not execution_result['success']:
                return self._create_error_response(response, "Query execution failed", execution_result.get('error'))
            
            query_results = execution_result['results']
            response['query_results'] = query_results
            response['execution_time_ms'] = execution_result.get('execution_time_ms', 0)
            response['processing_steps'].append({
                'step': 5,
                'agent': 'sql_executor',
                'status': 'completed',
                'duration_ms': int((time.time() - step_start) * 1000)
            })
            
            # Step 6: Apply data masking
            step_start = time.time()
            logger.info("🎭 Step 6: Applying data masking...")
            masked_results = self._apply_data_masking(query_results, role_restrictions)
            response['masked_results'] = masked_results
            response['processing_steps'].append({
                'step': 6,
                'agent': 'data_masking',
                'status': 'completed',
                'duration_ms': int((time.time() - step_start) * 1000)
            })
            
            # Step 7: Generate natural language answer
            step_start = time.time()
            logger.info("📝 Step 7: Generating answer...")
            answer_result = self.answer_maker_agent.generate_answer(
                user_query, masked_results, validated_sql, {
                    'execution_time_ms': response['execution_time_ms'],
                    'validation_passed': True
                }
            )
            
            if not answer_result['success']:
                return self._create_error_response(response, "Failed to generate answer", answer_result.get('error'))
            
            response['answer'] = answer_result['answer']
            response['answer_metadata'] = answer_result.get('answer_metadata', {})
            response['processing_steps'].append({
                'step': 7,
                'agent': 'answer_maker',
                'status': 'completed',
                'duration_ms': int((time.time() - step_start) * 1000)
            })
            
            # Step 8: Create visualization (if needed)
            visualization_plan = execution_plan.get('visualization_plan', {})
            if visualization_plan.get('requires_visualization'):
                step_start = time.time()
                logger.info("📊 Step 8: Creating visualization...")
                viz_result = self.graph_agent.create_visualization(
                    masked_results, visualization_plan, user_query
                )
                
                response['visualization'] = viz_result
                response['processing_steps'].append({
                    'step': 8,
                    'agent': 'graph_generator',
                    'status': 'completed',
                    'duration_ms': int((time.time() - step_start) * 1000)
                })
            
            # Step 9: Quality assurance
            step_start = time.time()
            logger.info("🔍 Step 9: Quality assurance...")
            qa_result = self.qa_agent.validate_response(
                user_query, response['answer'], masked_results, {
                    'execution_time_ms': response['execution_time_ms']
                }
            )
            
            response['quality_assessment'] = qa_result.get('quality_assessment', {})
            response['processing_steps'].append({
                'step': 9,
                'agent': 'qa_validator',
                'status': 'completed',
                'duration_ms': int((time.time() - step_start) * 1000)
            })
            
            # Finalize response
            total_time = time.time() - start_time
            response['success'] = True
            response['execution_metadata'].update({
                'completed_at': datetime.utcnow().isoformat(),
                'total_processing_time_ms': int(total_time * 1000),
                'steps_completed': len(response['processing_steps'])
            })
            
            logger.info(f"🎉 Agentic processing completed successfully in {total_time:.2f}s")
            return response
            
        except Exception as e:
            logger.error(f"Error in agentic processing: {str(e)}")
            logger.error(traceback.format_exc())
            return self._create_error_response(response, "Agentic processing failed", str(e))
    
    def _prepare_role_context(self, role: DatabaseRole) -> Dict[str, Any]:
        """Prepare role schema and semantic context"""
        try:
            # Generate role-specific schema
            role_schema = self.schema_generator.generate_role_schema(role)
            
            # Get semantic summaries
            semantic_summaries = self.schema_generator.get_role_semantic_context(role)
            
            # Prepare role restrictions
            role_restrictions = {
                'where_conditions': role.where_conditions,
                'allowed_tables': role.allowed_tables,
                'restricted_tables': role.restricted_tables,
                'column_masking_rules': role.column_masking_rules or {}
            }
            
            return {
                'success': True,
                'role_schema': role_schema,
                'semantic_summaries': semantic_summaries,
                'role_restrictions': role_restrictions
            }
            
        except Exception as e:
            logger.error(f"Error preparing role context: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_sql_query(self, sql_query: str, role: DatabaseRole, 
                          user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute SQL query against the database (simulated for now)"""
        try:
            # This is a simulation - in production, this would connect to the actual database
            # and execute the query using the database connection utilities
            
            logger.info("Simulating SQL query execution...")
            
            # Simulate execution time
            execution_time = 150  # milliseconds
            
            # Simulate results based on query type
            if 'COUNT' in sql_query.upper():
                results = [{'count': 42}]
            elif 'SELECT' in sql_query.upper():
                # Simulate table data
                results = [
                    {'id': 1, 'name': 'Sample Data 1', 'value': 100},
                    {'id': 2, 'name': 'Sample Data 2', 'value': 200},
                    {'id': 3, 'name': 'Sample Data 3', 'value': 150}
                ]
            else:
                results = []
            
            return {
                'success': True,
                'results': results,
                'execution_time_ms': execution_time,
                'rows_affected': len(results)
            }
            
        except Exception as e:
            logger.error(f"Error executing SQL query: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _apply_data_masking(self, query_results: List[Dict[str, Any]], 
                           role_restrictions: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Apply data masking to query results"""
        try:
            masking_rules = role_restrictions.get('column_masking_rules', {})
            if not masking_rules:
                return query_results
            
            # Apply masking to results
            # For now, we'll assume all results are from the first table mentioned in masking rules
            table_name = list(masking_rules.keys())[0] if masking_rules else None
            
            if table_name:
                masked_results = self.masking_engine.apply_masking_to_results(
                    query_results, table_name, masking_rules
                )
                return masked_results
            
            return query_results
            
        except Exception as e:
            logger.error(f"Error applying data masking: {str(e)}")
            return query_results
    
    def _create_error_response(self, response: Dict[str, Any], error_message: str, 
                              error_details: str = None) -> Dict[str, Any]:
        """Create standardized error response"""
        response.update({
            'success': False,
            'error': error_message,
            'error_details': error_details,
            'execution_metadata': {
                **response.get('execution_metadata', {}),
                'failed_at': datetime.utcnow().isoformat(),
                'total_processing_time_ms': int((time.time() - time.time()) * 1000)
            }
        })
        return response
