"""
Database Chat SQL Agent

This agent generates SQL queries based on execution plans and user queries.
It uses the role schema and semantic summaries to create accurate, role-compliant SQL.
"""

import logging
import traceback
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from utils.openai_utils import openai_client
from config.settings import AZURE_OPENAI_DEPLOYMENT_NAME

logger = logging.getLogger(__name__)

class DatabaseChatSQLAgent:
    """
    SQL generation agent that converts execution plans to SQL queries
    """
    
    def __init__(self):
        self.client = openai_client
        self.model = AZURE_OPENAI_DEPLOYMENT_NAME
        self.max_tokens = 2500
    
    def generate_sql(self, user_query: str, execution_plan: Dict[str, Any], 
                    role_schema: Dict[str, Any], semantic_summaries: Dict[str, Any],
                    role_restrictions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate SQL query based on execution plan and context
        
        Args:
            user_query: Original user query
            execution_plan: Execution plan from planner agent
            role_schema: Filtered schema for the user's role
            semantic_summaries: Semantic summaries for context
            role_restrictions: Role-specific restrictions and where conditions
            
        Returns:
            Dictionary containing SQL query and metadata
        """
        try:
            logger.info(f"🔧 Generating SQL for query: {user_query[:100]}...")
            
            # Extract relevant information from execution plan
            query_analysis = execution_plan.get('query_analysis', {})
            table_analysis = execution_plan.get('table_analysis', {})
            
            # Build context for SQL generation
            sql_context = self._build_sql_context(
                role_schema, semantic_summaries, table_analysis, role_restrictions
            )
            
            # Generate the SQL query
            sql_result = self._generate_sql_query(
                user_query, query_analysis, table_analysis, sql_context
            )
            
            if not sql_result.get('success'):
                return sql_result
            
            sql_query = sql_result['sql_query']
            
            # Enhance the SQL with role restrictions
            enhanced_sql = self._apply_role_restrictions(sql_query, role_restrictions)
            
            # Validate SQL syntax
            validation_result = self._validate_sql_syntax(enhanced_sql)
            
            # Extract metadata from the generated SQL
            sql_metadata = self._extract_sql_metadata(enhanced_sql, table_analysis)
            
            result = {
                'success': True,
                'sql_query': enhanced_sql,
                'original_sql': sql_query,
                'metadata': sql_metadata,
                'validation': validation_result,
                'tables_used': sql_metadata.get('tables_used', []),
                'columns_used': sql_metadata.get('columns_used', {}),
                'query_type': sql_metadata.get('query_type', 'SELECT'),
                'estimated_complexity': self._estimate_sql_complexity(enhanced_sql),
                'generated_at': datetime.utcnow().isoformat()
            }
            
            logger.info(f"🔧 SQL generated successfully: {len(enhanced_sql)} characters")
            return result
            
        except Exception as e:
            logger.error(f"Error generating SQL: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e)
            }
    
    def _build_sql_context(self, role_schema: Dict[str, Any], 
                          semantic_summaries: Dict[str, Any],
                          table_analysis: Dict[str, Any],
                          role_restrictions: Dict[str, Any]) -> str:
        """Build comprehensive context for SQL generation"""
        try:
            context_parts = []
            
            # Add database type information
            db_info = role_schema.get('database_info', {})
            if db_info.get('database_type'):
                context_parts.append(f"Database Type: {db_info['database_type']}")
            
            # Add role restrictions
            if role_restrictions.get('where_conditions'):
                context_parts.append(f"Role Restrictions: {role_restrictions['where_conditions']}")
            
            # Add table information for primary tables
            primary_tables = table_analysis.get('primary_tables', [])
            secondary_tables = table_analysis.get('secondary_tables', [])
            all_tables = primary_tables + secondary_tables
            
            context_parts.append("\nAvailable Tables:")
            
            tables = role_schema.get('tables', {})
            for table_name in all_tables:
                if table_name in tables:
                    table_info = tables[table_name]
                    context_parts.append(f"\nTable: {table_name}")
                    
                    # Add semantic summary
                    table_summary_key = f"table_{table_name}"
                    if table_summary_key in semantic_summaries:
                        summary = semantic_summaries[table_summary_key]
                        context_parts.append(f"Purpose: {summary.get('summary', '')[:150]}")
                    
                    # Add columns
                    columns = table_info.get('columns', [])
                    context_parts.append("Columns:")
                    for col in columns:
                        col_desc = f"  - {col['name']} ({col['type']})"
                        if col.get('primary_key'):
                            col_desc += " [PRIMARY KEY]"
                        if not col.get('nullable', True):
                            col_desc += " [NOT NULL]"
                        if col.get('masked'):
                            col_desc += f" [MASKED: {col.get('mask_type', 'unknown')}]"
                        context_parts.append(col_desc)
                    
                    # Add relationships
                    foreign_keys = table_info.get('foreign_keys', [])
                    if foreign_keys:
                        context_parts.append("Foreign Keys:")
                        for fk in foreign_keys:
                            context_parts.append(
                                f"  - {fk.get('constrained_columns', [])} -> "
                                f"{fk.get('referred_table')}.{fk.get('referred_columns', [])}"
                            )
            
            # Add join relationships from analysis
            join_relationships = table_analysis.get('join_relationships', [])
            if join_relationships:
                context_parts.append("\nSuggested Joins:")
                for join in join_relationships:
                    context_parts.append(
                        f"  - {join.get('join_type', 'INNER')} JOIN {join.get('to_table')} "
                        f"ON {join.get('join_condition', '')}"
                    )
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error building SQL context: {str(e)}")
            return "No context available"
    
    def _generate_sql_query(self, user_query: str, query_analysis: Dict[str, Any],
                           table_analysis: Dict[str, Any], sql_context: str) -> Dict[str, Any]:
        """Generate the actual SQL query using LLM"""
        try:
            prompt = f"""
Generate a SQL query to answer the user's question based on the provided database schema and context.

User Question: "{user_query}"

Query Analysis:
- Intent: {query_analysis.get('intent_type', 'data_retrieval')}
- Complexity: {query_analysis.get('query_complexity', 'medium')}
- Requires Aggregation: {query_analysis.get('requires_aggregation', False)}
- Requires Joins: {query_analysis.get('requires_joins', False)}
- Requires Grouping: {query_analysis.get('requires_grouping', False)}
- Time Dimension: {query_analysis.get('time_dimension', False)}

Database Schema and Context:
{sql_context}

Requirements:
1. Generate a valid SQL query that answers the user's question
2. Use only the tables and columns available in the schema
3. Follow SQL best practices (proper joins, appropriate WHERE clauses, etc.)
4. If aggregation is needed, include appropriate GROUP BY clauses
5. If sorting is implied, include ORDER BY clauses
6. Limit results to reasonable numbers (use LIMIT if appropriate)
7. Handle NULL values appropriately
8. Use proper SQL syntax for the database type

Return only the SQL query, no explanations or markdown formatting.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert SQL developer. Generate only valid SQL queries."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.1
            )
            
            sql_query = response.choices[0].message.content.strip()
            
            # Clean up the SQL query
            sql_query = self._clean_sql_query(sql_query)
            
            return {
                'success': True,
                'sql_query': sql_query
            }
            
        except Exception as e:
            logger.error(f"Error generating SQL query: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _clean_sql_query(self, sql_query: str) -> str:
        """Clean and format the SQL query"""
        try:
            # Remove markdown formatting if present
            sql_query = re.sub(r'```sql\s*', '', sql_query)
            sql_query = re.sub(r'```\s*', '', sql_query)
            
            # Remove extra whitespace
            sql_query = re.sub(r'\s+', ' ', sql_query)
            sql_query = sql_query.strip()
            
            # Ensure query ends with semicolon
            if not sql_query.endswith(';'):
                sql_query += ';'
            
            return sql_query
            
        except Exception as e:
            logger.error(f"Error cleaning SQL query: {str(e)}")
            return sql_query
    
    def _apply_role_restrictions(self, sql_query: str, role_restrictions: Dict[str, Any]) -> str:
        """Apply role-specific restrictions to the SQL query"""
        try:
            where_conditions = role_restrictions.get('where_conditions')
            if not where_conditions:
                return sql_query
            
            # Parse the SQL to add WHERE conditions
            # This is a simplified implementation - could be improved with proper SQL parsing
            
            # Remove trailing semicolon for modification
            sql_query = sql_query.rstrip(';')
            
            # Check if query already has WHERE clause
            if 'WHERE' in sql_query.upper():
                # Add role restrictions with AND
                enhanced_sql = f"{sql_query} AND ({where_conditions})"
            else:
                # Add WHERE clause with role restrictions
                enhanced_sql = f"{sql_query} WHERE {where_conditions}"
            
            # Add semicolon back
            enhanced_sql += ';'
            
            logger.info(f"Applied role restrictions: {where_conditions}")
            return enhanced_sql
            
        except Exception as e:
            logger.error(f"Error applying role restrictions: {str(e)}")
            return sql_query
    
    def _validate_sql_syntax(self, sql_query: str) -> Dict[str, Any]:
        """Validate SQL syntax (basic validation)"""
        try:
            validation_result = {
                'is_valid': True,
                'errors': [],
                'warnings': []
            }
            
            # Basic syntax checks
            sql_upper = sql_query.upper()
            
            # Check for required SELECT
            if not sql_upper.startswith('SELECT'):
                validation_result['errors'].append("Query must start with SELECT")
                validation_result['is_valid'] = False
            
            # Check for balanced parentheses
            if sql_query.count('(') != sql_query.count(')'):
                validation_result['errors'].append("Unbalanced parentheses")
                validation_result['is_valid'] = False
            
            # Check for dangerous operations (for safety)
            dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
            for keyword in dangerous_keywords:
                if keyword in sql_upper:
                    validation_result['errors'].append(f"Dangerous operation detected: {keyword}")
                    validation_result['is_valid'] = False
            
            # Warnings for potential issues
            if 'SELECT *' in sql_upper:
                validation_result['warnings'].append("Using SELECT * - consider specifying columns")
            
            if 'LIMIT' not in sql_upper and 'TOP' not in sql_upper:
                validation_result['warnings'].append("No LIMIT clause - query might return many rows")
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating SQL syntax: {str(e)}")
            return {
                'is_valid': False,
                'errors': [str(e)],
                'warnings': []
            }
    
    def _extract_sql_metadata(self, sql_query: str, table_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from the SQL query"""
        try:
            metadata = {
                'query_type': 'SELECT',
                'tables_used': [],
                'columns_used': {},
                'has_joins': False,
                'has_aggregation': False,
                'has_groupby': False,
                'has_orderby': False,
                'has_limit': False
            }
            
            sql_upper = sql_query.upper()
            
            # Extract tables used
            tables_used = set()
            
            # From table analysis
            primary_tables = table_analysis.get('primary_tables', [])
            secondary_tables = table_analysis.get('secondary_tables', [])
            for table in primary_tables + secondary_tables:
                if table.upper() in sql_upper:
                    tables_used.add(table)
            
            metadata['tables_used'] = list(tables_used)
            
            # Check for SQL features
            metadata['has_joins'] = any(join_type in sql_upper for join_type in ['JOIN', 'INNER JOIN', 'LEFT JOIN', 'RIGHT JOIN'])
            metadata['has_aggregation'] = any(agg_func in sql_upper for agg_func in ['COUNT', 'SUM', 'AVG', 'MAX', 'MIN'])
            metadata['has_groupby'] = 'GROUP BY' in sql_upper
            metadata['has_orderby'] = 'ORDER BY' in sql_upper
            metadata['has_limit'] = 'LIMIT' in sql_upper or 'TOP' in sql_upper
            
            # Extract columns (simplified - could be improved)
            required_columns = table_analysis.get('required_columns', {})
            metadata['columns_used'] = required_columns
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting SQL metadata: {str(e)}")
            return {}
    
    def _estimate_sql_complexity(self, sql_query: str) -> str:
        """Estimate the complexity of the SQL query"""
        try:
            complexity_score = 0
            sql_upper = sql_query.upper()
            
            # Base complexity
            complexity_score += 1
            
            # Add complexity for various features
            if 'JOIN' in sql_upper:
                complexity_score += 2
                # Multiple joins
                complexity_score += sql_upper.count('JOIN') - 1
            
            if 'GROUP BY' in sql_upper:
                complexity_score += 2
            
            if 'HAVING' in sql_upper:
                complexity_score += 1
            
            if 'UNION' in sql_upper:
                complexity_score += 2
            
            if 'SUBQUERY' in sql_upper or '(' in sql_query:
                complexity_score += 3
            
            # Aggregate functions
            agg_functions = ['COUNT', 'SUM', 'AVG', 'MAX', 'MIN']
            for func in agg_functions:
                complexity_score += sql_upper.count(func) * 0.5
            
            # Determine complexity level
            if complexity_score <= 2:
                return 'low'
            elif complexity_score <= 5:
                return 'medium'
            else:
                return 'high'
                
        except Exception as e:
            logger.error(f"Error estimating SQL complexity: {str(e)}")
            return 'medium'
    
    def refine_sql_query(self, sql_query: str, feedback: str, 
                        role_schema: Dict[str, Any]) -> Dict[str, Any]:
        """Refine SQL query based on feedback"""
        try:
            logger.info(f"🔧 Refining SQL query based on feedback: {feedback[:100]}...")
            
            prompt = f"""
Improve the following SQL query based on the provided feedback.

Current SQL Query:
{sql_query}

Feedback:
{feedback}

Available Schema:
{self._get_schema_summary(role_schema)}

Please provide an improved SQL query that addresses the feedback.
Return only the SQL query, no explanations.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert SQL developer. Improve SQL queries based on feedback."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1500,
                temperature=0.1
            )
            
            refined_sql = response.choices[0].message.content.strip()
            refined_sql = self._clean_sql_query(refined_sql)
            
            return {
                'success': True,
                'refined_sql': refined_sql,
                'original_sql': sql_query
            }
            
        except Exception as e:
            logger.error(f"Error refining SQL query: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _get_schema_summary(self, role_schema: Dict[str, Any]) -> str:
        """Get a brief summary of the schema for context"""
        try:
            tables = role_schema.get('tables', {})
            summary_parts = []
            
            for table_name, table_info in tables.items():
                columns = [col['name'] for col in table_info.get('columns', [])]
                summary_parts.append(f"{table_name}: {', '.join(columns[:5])}")
                if len(columns) > 5:
                    summary_parts[-1] += f" (and {len(columns) - 5} more)"
            
            return "\n".join(summary_parts)
            
        except Exception as e:
            logger.error(f"Error getting schema summary: {str(e)}")
            return "Schema information not available"
