"""
Database Chat Validator Agent

This agent validates SQL queries against role permissions and security restrictions.
It ensures queries comply with role-based access control and data masking requirements.
"""

import logging
import traceback
import re
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseChatValidatorAgent:
    """
    Validator agent that ensures SQL queries comply with role restrictions
    """
    
    def __init__(self):
        pass
    
    def validate_sql_query(self, sql_query: str, role_schema: Dict[str, Any], 
                          role_restrictions: Dict[str, Any],
                          user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate SQL query against role permissions and restrictions
        
        Args:
            sql_query: The SQL query to validate
            role_schema: The filtered schema for the user's role
            role_restrictions: Role-specific restrictions
            user_context: User and session context
            
        Returns:
            Dictionary containing validation results and any modifications
        """
        try:
            logger.info(f"🔍 Validating SQL query for role compliance...")
            
            validation_result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'security_issues': [],
                'modifications_made': [],
                'validated_sql': sql_query,
                'risk_level': 'low',
                'validation_timestamp': datetime.utcnow().isoformat()
            }
            
            # 1. Basic SQL syntax and security validation
            syntax_validation = self._validate_sql_syntax(sql_query)
            validation_result.update(syntax_validation)
            
            # 2. Table access validation
            table_validation = self._validate_table_access(sql_query, role_schema, role_restrictions)
            self._merge_validation_results(validation_result, table_validation)
            
            # 3. Column access validation
            column_validation = self._validate_column_access(sql_query, role_schema, role_restrictions)
            self._merge_validation_results(validation_result, column_validation)
            
            # 4. Row-level security validation
            row_validation = self._validate_row_level_security(sql_query, role_restrictions)
            self._merge_validation_results(validation_result, row_validation)
            
            # 5. Query complexity and resource validation
            resource_validation = self._validate_resource_usage(sql_query, user_context)
            self._merge_validation_results(validation_result, resource_validation)
            
            # 6. Data masking requirements
            masking_validation = self._validate_masking_requirements(sql_query, role_restrictions)
            self._merge_validation_results(validation_result, masking_validation)
            
            # 7. Apply any necessary modifications
            if validation_result['is_valid']:
                modified_sql = self._apply_security_modifications(
                    sql_query, role_restrictions, validation_result['modifications_made']
                )
                validation_result['validated_sql'] = modified_sql
            
            # 8. Calculate overall risk level
            validation_result['risk_level'] = self._calculate_risk_level(validation_result)
            
            logger.info(f"🔍 Validation completed: {'PASSED' if validation_result['is_valid'] else 'FAILED'}")
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating SQL query: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'is_valid': False,
                'errors': [f"Validation error: {str(e)}"],
                'warnings': [],
                'security_issues': [],
                'modifications_made': [],
                'validated_sql': sql_query,
                'risk_level': 'high',
                'validation_timestamp': datetime.utcnow().isoformat()
            }
    
    def _validate_sql_syntax(self, sql_query: str) -> Dict[str, Any]:
        """Validate basic SQL syntax and security"""
        try:
            result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'security_issues': []
            }
            
            sql_upper = sql_query.upper().strip()
            
            # Check for dangerous operations
            dangerous_operations = [
                'DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 
                'TRUNCATE', 'GRANT', 'REVOKE', 'EXEC', 'EXECUTE'
            ]
            
            for operation in dangerous_operations:
                if f' {operation} ' in f' {sql_upper} ' or sql_upper.startswith(operation):
                    result['security_issues'].append(f"Dangerous operation detected: {operation}")
                    result['is_valid'] = False
            
            # Check for SQL injection patterns
            injection_patterns = [
                r"';.*--",  # Comment injection
                r"UNION.*SELECT",  # Union injection
                r"OR.*1.*=.*1",  # Boolean injection
                r"AND.*1.*=.*1",  # Boolean injection
                r"EXEC\s*\(",  # Command execution
                r"xp_cmdshell",  # SQL Server command shell
            ]
            
            for pattern in injection_patterns:
                if re.search(pattern, sql_upper, re.IGNORECASE):
                    result['security_issues'].append(f"Potential SQL injection pattern detected")
                    result['is_valid'] = False
            
            # Check for required SELECT statement
            if not sql_upper.startswith('SELECT'):
                result['errors'].append("Only SELECT statements are allowed")
                result['is_valid'] = False
            
            # Check for balanced parentheses
            if sql_query.count('(') != sql_query.count(')'):
                result['errors'].append("Unbalanced parentheses in SQL query")
                result['is_valid'] = False
            
            # Warnings for potential issues
            if 'SELECT *' in sql_upper:
                result['warnings'].append("Using SELECT * - consider specifying columns for better performance")
            
            if 'LIMIT' not in sql_upper and 'TOP' not in sql_upper:
                result['warnings'].append("No row limit specified - query might return large result sets")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in SQL syntax validation: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"Syntax validation error: {str(e)}"],
                'warnings': [],
                'security_issues': []
            }
    
    def _validate_table_access(self, sql_query: str, role_schema: Dict[str, Any], 
                              role_restrictions: Dict[str, Any]) -> Dict[str, Any]:
        """Validate that user can access the tables in the query"""
        try:
            result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'security_issues': []
            }
            
            # Extract tables from SQL query
            tables_in_query = self._extract_tables_from_sql(sql_query)
            
            # Get allowed tables from role schema
            allowed_tables = set(role_schema.get('tables', {}).keys())
            
            # Check if all tables in query are allowed
            unauthorized_tables = tables_in_query - allowed_tables
            
            if unauthorized_tables:
                result['errors'].append(
                    f"Access denied to tables: {', '.join(unauthorized_tables)}"
                )
                result['security_issues'].append(
                    f"Attempted access to unauthorized tables: {', '.join(unauthorized_tables)}"
                )
                result['is_valid'] = False
            
            # Check against explicitly restricted tables
            restricted_tables = set(role_restrictions.get('restricted_tables', []))
            restricted_access = tables_in_query & restricted_tables
            
            if restricted_access:
                result['errors'].append(
                    f"Access denied to restricted tables: {', '.join(restricted_access)}"
                )
                result['security_issues'].append(
                    f"Attempted access to restricted tables: {', '.join(restricted_access)}"
                )
                result['is_valid'] = False
            
            # Check allowed tables if specified
            allowed_tables_restriction = role_restrictions.get('allowed_tables')
            if allowed_tables_restriction:
                allowed_set = set(allowed_tables_restriction)
                unauthorized_by_allowlist = tables_in_query - allowed_set
                
                if unauthorized_by_allowlist:
                    result['errors'].append(
                        f"Tables not in allowed list: {', '.join(unauthorized_by_allowlist)}"
                    )
                    result['is_valid'] = False
            
            return result
            
        except Exception as e:
            logger.error(f"Error in table access validation: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"Table access validation error: {str(e)}"],
                'warnings': [],
                'security_issues': []
            }
    
    def _validate_column_access(self, sql_query: str, role_schema: Dict[str, Any], 
                               role_restrictions: Dict[str, Any]) -> Dict[str, Any]:
        """Validate column access and masking requirements"""
        try:
            result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'security_issues': [],
                'modifications_made': []
            }
            
            # Extract columns from SQL query (simplified)
            columns_in_query = self._extract_columns_from_sql(sql_query)
            
            # Get column masking rules
            masking_rules = role_restrictions.get('column_masking_rules', {})
            
            # Check for hidden columns
            for table_name, columns in columns_in_query.items():
                if table_name in masking_rules:
                    table_masking = masking_rules[table_name]
                    
                    for column in columns:
                        if column in table_masking:
                            mask_type = table_masking[column]
                            
                            if mask_type == 'hide':
                                result['errors'].append(
                                    f"Access denied to hidden column: {table_name}.{column}"
                                )
                                result['security_issues'].append(
                                    f"Attempted access to hidden column: {table_name}.{column}"
                                )
                                result['is_valid'] = False
                            else:
                                result['warnings'].append(
                                    f"Column {table_name}.{column} will be masked ({mask_type})"
                                )
                                result['modifications_made'].append(
                                    f"Apply {mask_type} masking to {table_name}.{column}"
                                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in column access validation: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"Column access validation error: {str(e)}"],
                'warnings': [],
                'security_issues': [],
                'modifications_made': []
            }
    
    def _validate_row_level_security(self, sql_query: str, 
                                   role_restrictions: Dict[str, Any]) -> Dict[str, Any]:
        """Validate row-level security restrictions"""
        try:
            result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'security_issues': [],
                'modifications_made': []
            }
            
            where_conditions = role_restrictions.get('where_conditions')
            if not where_conditions:
                return result
            
            sql_upper = sql_query.upper()
            
            # Check if role restrictions are already applied
            if where_conditions.upper() not in sql_upper:
                result['modifications_made'].append(
                    f"Apply row-level security: {where_conditions}"
                )
                result['warnings'].append(
                    "Row-level security restrictions will be applied"
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in row-level security validation: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"Row-level security validation error: {str(e)}"],
                'warnings': [],
                'security_issues': [],
                'modifications_made': []
            }
    
    def _validate_resource_usage(self, sql_query: str, user_context: Dict[str, Any]) -> Dict[str, Any]:
        """Validate query resource usage and complexity"""
        try:
            result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'security_issues': []
            }
            
            sql_upper = sql_query.upper()
            
            # Check for potentially expensive operations
            expensive_operations = [
                ('CROSS JOIN', 'Cross joins can be very expensive'),
                ('CARTESIAN', 'Cartesian products can be very expensive'),
                ('SELECT DISTINCT', 'DISTINCT operations can be expensive on large datasets')
            ]
            
            for operation, warning in expensive_operations:
                if operation in sql_upper:
                    result['warnings'].append(warning)
            
            # Check for missing LIMIT clause on potentially large results
            if ('JOIN' in sql_upper or 'GROUP BY' in sql_upper) and 'LIMIT' not in sql_upper:
                result['warnings'].append(
                    "Query might return large result set - consider adding LIMIT clause"
                )
            
            # Check for nested subqueries (complexity)
            subquery_count = sql_query.count('(SELECT')
            if subquery_count > 3:
                result['warnings'].append(
                    f"Query has {subquery_count} subqueries - high complexity"
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in resource usage validation: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"Resource usage validation error: {str(e)}"],
                'warnings': [],
                'security_issues': []
            }
    
    def _validate_masking_requirements(self, sql_query: str, 
                                     role_restrictions: Dict[str, Any]) -> Dict[str, Any]:
        """Validate data masking requirements"""
        try:
            result = {
                'is_valid': True,
                'errors': [],
                'warnings': [],
                'security_issues': [],
                'modifications_made': []
            }
            
            masking_rules = role_restrictions.get('column_masking_rules', {})
            if not masking_rules:
                return result
            
            # Check if query selects masked columns
            columns_in_query = self._extract_columns_from_sql(sql_query)
            
            for table_name, columns in columns_in_query.items():
                if table_name in masking_rules:
                    table_masking = masking_rules[table_name]
                    
                    for column in columns:
                        if column in table_masking and table_masking[column] != 'hide':
                            mask_type = table_masking[column]
                            result['modifications_made'].append(
                                f"Apply {mask_type} masking to {table_name}.{column} in results"
                            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in masking validation: {str(e)}")
            return {
                'is_valid': False,
                'errors': [f"Masking validation error: {str(e)}"],
                'warnings': [],
                'security_issues': [],
                'modifications_made': []
            }
    
    def _extract_tables_from_sql(self, sql_query: str) -> Set[str]:
        """Extract table names from SQL query"""
        try:
            tables = set()
            sql_upper = sql_query.upper()
            
            # Find FROM clauses
            from_matches = re.finditer(r'FROM\s+([a-zA-Z_][a-zA-Z0-9_]*)', sql_upper)
            for match in from_matches:
                tables.add(match.group(1).lower())
            
            # Find JOIN clauses
            join_matches = re.finditer(r'JOIN\s+([a-zA-Z_][a-zA-Z0-9_]*)', sql_upper)
            for match in join_matches:
                tables.add(match.group(1).lower())
            
            return tables
            
        except Exception as e:
            logger.error(f"Error extracting tables from SQL: {str(e)}")
            return set()
    
    def _extract_columns_from_sql(self, sql_query: str) -> Dict[str, List[str]]:
        """Extract column references from SQL query (simplified)"""
        try:
            # This is a simplified implementation
            # A full implementation would use a proper SQL parser
            
            columns_by_table = {}
            
            # Look for table.column patterns
            column_matches = re.finditer(r'([a-zA-Z_][a-zA-Z0-9_]*)\.([a-zA-Z_][a-zA-Z0-9_]*)', sql_query)
            
            for match in column_matches:
                table_name = match.group(1).lower()
                column_name = match.group(2).lower()
                
                if table_name not in columns_by_table:
                    columns_by_table[table_name] = []
                
                if column_name not in columns_by_table[table_name]:
                    columns_by_table[table_name].append(column_name)
            
            return columns_by_table
            
        except Exception as e:
            logger.error(f"Error extracting columns from SQL: {str(e)}")
            return {}
    
    def _apply_security_modifications(self, sql_query: str, role_restrictions: Dict[str, Any],
                                    modifications: List[str]) -> str:
        """Apply security modifications to the SQL query"""
        try:
            modified_sql = sql_query
            
            # Apply row-level security
            where_conditions = role_restrictions.get('where_conditions')
            if where_conditions:
                # Check if WHERE clause exists
                if 'WHERE' in modified_sql.upper():
                    # Add to existing WHERE clause
                    modified_sql = modified_sql.rstrip(';')
                    modified_sql += f" AND ({where_conditions});"
                else:
                    # Add new WHERE clause
                    modified_sql = modified_sql.rstrip(';')
                    modified_sql += f" WHERE {where_conditions};"
            
            return modified_sql
            
        except Exception as e:
            logger.error(f"Error applying security modifications: {str(e)}")
            return sql_query
    
    def _merge_validation_results(self, main_result: Dict[str, Any], 
                                 additional_result: Dict[str, Any]):
        """Merge additional validation results into main result"""
        try:
            if not additional_result.get('is_valid', True):
                main_result['is_valid'] = False
            
            main_result['errors'].extend(additional_result.get('errors', []))
            main_result['warnings'].extend(additional_result.get('warnings', []))
            main_result['security_issues'].extend(additional_result.get('security_issues', []))
            main_result['modifications_made'].extend(additional_result.get('modifications_made', []))
            
        except Exception as e:
            logger.error(f"Error merging validation results: {str(e)}")
    
    def _calculate_risk_level(self, validation_result: Dict[str, Any]) -> str:
        """Calculate overall risk level based on validation results"""
        try:
            if not validation_result['is_valid']:
                return 'high'
            
            security_issues = len(validation_result.get('security_issues', []))
            errors = len(validation_result.get('errors', []))
            warnings = len(validation_result.get('warnings', []))
            
            if security_issues > 0 or errors > 0:
                return 'high'
            elif warnings > 3:
                return 'medium'
            elif warnings > 0:
                return 'low'
            else:
                return 'minimal'
                
        except Exception as e:
            logger.error(f"Error calculating risk level: {str(e)}")
            return 'medium'
