"""
Database Chat Answer Maker Agent

This agent generates natural language answers from query results.
It creates comprehensive, contextual responses that explain the data findings.
"""

import logging
import traceback
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from utils.openai_utils import openai_client
from config.settings import AZURE_OPENAI_DEPLOYMENT_NAME

logger = logging.getLogger(__name__)

class DatabaseChatAnswerMakerAgent:
    """
    Answer maker agent that generates natural language answers from query results
    """
    
    def __init__(self):
        self.client = openai_client
        self.model = AZURE_OPENAI_DEPLOYMENT_NAME
        self.max_tokens = 2000
    
    def generate_answer(self, user_query: str, query_results: List[Dict[str, Any]], 
                       sql_query: str, execution_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate natural language answer from query results
        
        Args:
            user_query: Original user question
            query_results: Results from SQL query execution
            sql_query: The SQL query that was executed
            execution_context: Additional context about the query execution
            
        Returns:
            Dictionary containing the generated answer and metadata
        """
        try:
            logger.info(f"📝 Generating answer for query with {len(query_results)} results...")
            
            # Analyze the query results
            results_analysis = self._analyze_query_results(query_results)
            
            # Generate the natural language answer
            answer_result = self._generate_natural_language_answer(
                user_query, query_results, sql_query, results_analysis, execution_context
            )
            
            if not answer_result.get('success'):
                return answer_result
            
            # Enhance the answer with insights
            enhanced_answer = self._enhance_answer_with_insights(
                answer_result['answer'], results_analysis, execution_context
            )
            
            # Format the final response
            final_response = {
                'success': True,
                'answer': enhanced_answer,
                'raw_answer': answer_result['answer'],
                'results_summary': results_analysis,
                'answer_metadata': {
                    'result_count': len(query_results),
                    'answer_length': len(enhanced_answer),
                    'confidence_score': self._calculate_confidence_score(query_results, execution_context),
                    'includes_insights': True,
                    'generated_at': datetime.utcnow().isoformat()
                }
            }
            
            logger.info(f"📝 Answer generated successfully: {len(enhanced_answer)} characters")
            return final_response
            
        except Exception as e:
            logger.error(f"Error generating answer: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e)
            }
    
    def _analyze_query_results(self, query_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze query results to understand the data structure and content"""
        try:
            if not query_results:
                return {
                    'result_count': 0,
                    'has_data': False,
                    'columns': [],
                    'data_types': {},
                    'summary_stats': {}
                }
            
            first_row = query_results[0]
            columns = list(first_row.keys())
            
            analysis = {
                'result_count': len(query_results),
                'has_data': True,
                'columns': columns,
                'data_types': {},
                'summary_stats': {},
                'sample_values': {}
            }
            
            # Analyze each column
            for column in columns:
                values = [row.get(column) for row in query_results if row.get(column) is not None]
                
                if not values:
                    continue
                
                # Determine data type
                sample_value = values[0]
                if isinstance(sample_value, (int, float)):
                    analysis['data_types'][column] = 'numeric'
                    # Calculate basic stats for numeric columns
                    numeric_values = [v for v in values if isinstance(v, (int, float))]
                    if numeric_values:
                        analysis['summary_stats'][column] = {
                            'min': min(numeric_values),
                            'max': max(numeric_values),
                            'avg': sum(numeric_values) / len(numeric_values),
                            'count': len(numeric_values)
                        }
                elif isinstance(sample_value, str):
                    analysis['data_types'][column] = 'text'
                    # Get unique values for text columns (limited)
                    unique_values = list(set(values))[:5]
                    analysis['sample_values'][column] = unique_values
                else:
                    analysis['data_types'][column] = 'other'
                
                # Store sample values
                if column not in analysis['sample_values']:
                    analysis['sample_values'][column] = values[:3]
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing query results: {str(e)}")
            return {
                'result_count': len(query_results) if query_results else 0,
                'has_data': bool(query_results),
                'columns': [],
                'data_types': {},
                'summary_stats': {}
            }
    
    def _generate_natural_language_answer(self, user_query: str, query_results: List[Dict[str, Any]],
                                        sql_query: str, results_analysis: Dict[str, Any],
                                        execution_context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate natural language answer using LLM"""
        try:
            # Prepare context for answer generation
            context = self._prepare_answer_context(query_results, results_analysis, execution_context)
            
            prompt = f"""
Generate a comprehensive, natural language answer to the user's question based on the query results.

User Question: "{user_query}"

SQL Query Executed: {sql_query}

Query Results Analysis:
- Number of results: {results_analysis.get('result_count', 0)}
- Columns: {', '.join(results_analysis.get('columns', []))}
- Data types: {results_analysis.get('data_types', {})}

{context}

Instructions:
1. Provide a direct answer to the user's question
2. Include specific numbers, names, and details from the results
3. Explain what the data shows in business terms
4. If there are interesting patterns or insights, mention them
5. If the results are empty, explain what this means
6. Keep the answer conversational but informative
7. Don't mention technical details like SQL or database tables unless relevant

Generate a clear, helpful answer that a business user would understand.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a data analyst who explains database results in clear, business-friendly language."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.3
            )
            
            answer = response.choices[0].message.content.strip()
            
            return {
                'success': True,
                'answer': answer
            }
            
        except Exception as e:
            logger.error(f"Error generating natural language answer: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _prepare_answer_context(self, query_results: List[Dict[str, Any]], 
                              results_analysis: Dict[str, Any],
                              execution_context: Dict[str, Any]) -> str:
        """Prepare context information for answer generation"""
        try:
            context_parts = []
            
            if not query_results:
                context_parts.append("No results were found for this query.")
                return "\n".join(context_parts)
            
            # Add sample results
            context_parts.append("Sample Results:")
            for i, row in enumerate(query_results[:5]):  # Show first 5 rows
                row_desc = ", ".join([f"{k}: {v}" for k, v in row.items()])
                context_parts.append(f"  Row {i+1}: {row_desc}")
            
            if len(query_results) > 5:
                context_parts.append(f"  ... and {len(query_results) - 5} more rows")
            
            # Add summary statistics
            summary_stats = results_analysis.get('summary_stats', {})
            if summary_stats:
                context_parts.append("\nSummary Statistics:")
                for column, stats in summary_stats.items():
                    context_parts.append(f"  {column}: min={stats['min']}, max={stats['max']}, avg={stats['avg']:.2f}")
            
            # Add execution context if available
            if execution_context.get('execution_time_ms'):
                context_parts.append(f"\nQuery executed in {execution_context['execution_time_ms']}ms")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error preparing answer context: {str(e)}")
            return "Query results available for analysis."
    
    def _enhance_answer_with_insights(self, base_answer: str, results_analysis: Dict[str, Any],
                                    execution_context: Dict[str, Any]) -> str:
        """Enhance the answer with additional insights and context"""
        try:
            enhanced_parts = [base_answer]
            
            # Add data quality insights
            result_count = results_analysis.get('result_count', 0)
            if result_count == 0:
                enhanced_parts.append("\n💡 No data was found matching your criteria. You might want to try adjusting your search parameters or time range.")
            elif result_count == 1:
                enhanced_parts.append("\n💡 This query returned a single result, which suggests very specific criteria were met.")
            elif result_count > 1000:
                enhanced_parts.append(f"\n💡 This query returned {result_count:,} results. Consider adding filters to narrow down the data if you're looking for specific information.")
            
            # Add performance insights
            execution_time = execution_context.get('execution_time_ms', 0)
            if execution_time > 5000:  # 5 seconds
                enhanced_parts.append(f"\n⏱️ This query took {execution_time/1000:.1f} seconds to execute. For faster results, consider adding more specific filters.")
            
            # Add data freshness context if available
            if execution_context.get('data_freshness'):
                enhanced_parts.append(f"\n📅 Data freshness: {execution_context['data_freshness']}")
            
            return "\n".join(enhanced_parts)
            
        except Exception as e:
            logger.error(f"Error enhancing answer with insights: {str(e)}")
            return base_answer
    
    def _calculate_confidence_score(self, query_results: List[Dict[str, Any]], 
                                  execution_context: Dict[str, Any]) -> float:
        """Calculate confidence score for the answer"""
        try:
            confidence = 0.8  # Base confidence
            
            # Adjust based on result count
            result_count = len(query_results) if query_results else 0
            
            if result_count == 0:
                confidence = 0.9  # High confidence in "no results"
            elif result_count == 1:
                confidence = 0.95  # High confidence in single result
            elif result_count <= 100:
                confidence = 0.9  # High confidence in reasonable result set
            elif result_count <= 1000:
                confidence = 0.8  # Good confidence
            else:
                confidence = 0.7  # Lower confidence for very large result sets
            
            # Adjust based on execution success
            if execution_context.get('validation_passed', True):
                confidence += 0.05
            
            if execution_context.get('execution_error'):
                confidence -= 0.3
            
            # Cap between 0 and 1
            return max(0.0, min(1.0, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating confidence score: {str(e)}")
            return 0.7
    
    def generate_summary_answer(self, user_query: str, query_results: List[Dict[str, Any]]) -> str:
        """Generate a brief summary answer for quick responses"""
        try:
            if not query_results:
                return "No results found for your query."
            
            result_count = len(query_results)
            
            if result_count == 1:
                # Single result - provide specific details
                row = query_results[0]
                key_values = []
                for key, value in row.items():
                    if value is not None:
                        key_values.append(f"{key}: {value}")
                
                return f"Found 1 result: {', '.join(key_values[:3])}"
            
            else:
                # Multiple results - provide summary
                columns = list(query_results[0].keys()) if query_results else []
                main_column = columns[0] if columns else "data"
                
                return f"Found {result_count:,} results. The data includes information about {main_column} and {len(columns)-1} other fields."
                
        except Exception as e:
            logger.error(f"Error generating summary answer: {str(e)}")
            return f"Found {len(query_results) if query_results else 0} results for your query."
