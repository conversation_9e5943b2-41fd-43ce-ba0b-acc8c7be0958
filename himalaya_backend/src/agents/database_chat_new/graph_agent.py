"""
Database Chat Graph Agent

This agent generates HTML visualizations from query results.
It creates charts, graphs, and other visual representations of data.
"""

import logging
import traceback
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseChatGraphAgent:
    """
    Graph agent that generates HTML visualizations from data
    """
    
    def __init__(self):
        pass
    
    def create_visualization(self, query_results: List[Dict[str, Any]], 
                           visualization_plan: Dict[str, Any],
                           user_query: str) -> Dict[str, Any]:
        """
        Create data visualization from query results
        
        Args:
            query_results: Results from SQL query execution
            visualization_plan: Plan from planner agent
            user_query: Original user query for context
            
        Returns:
            Dictionary containing visualization HTML and metadata
        """
        try:
            logger.info(f"📊 Creating visualization for {len(query_results)} data points...")
            
            if not query_results:
                return {
                    'success': True,
                    'visualization_html': self._create_no_data_visualization(),
                    'visualization_type': 'no_data',
                    'message': 'No data available for visualization'
                }
            
            # Determine the best visualization type
            viz_type = self._determine_visualization_type(query_results, visualization_plan)
            
            # Generate the visualization
            if viz_type == 'bar_chart':
                viz_html = self._create_bar_chart(query_results, visualization_plan)
            elif viz_type == 'line_chart':
                viz_html = self._create_line_chart(query_results, visualization_plan)
            elif viz_type == 'pie_chart':
                viz_html = self._create_pie_chart(query_results, visualization_plan)
            elif viz_type == 'table':
                viz_html = self._create_data_table(query_results, visualization_plan)
            else:
                viz_html = self._create_default_visualization(query_results, visualization_plan)
            
            result = {
                'success': True,
                'visualization_html': viz_html,
                'visualization_type': viz_type,
                'data_points': len(query_results),
                'chart_title': visualization_plan.get('chart_title', 'Data Visualization'),
                'generated_at': datetime.utcnow().isoformat()
            }
            
            logger.info(f"📊 Visualization created: {viz_type} with {len(query_results)} data points")
            return result
            
        except Exception as e:
            logger.error(f"Error creating visualization: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'visualization_html': self._create_error_visualization(str(e))
            }
    
    def _determine_visualization_type(self, query_results: List[Dict[str, Any]], 
                                    visualization_plan: Dict[str, Any]) -> str:
        """Determine the best visualization type for the data"""
        try:
            if not query_results:
                return 'no_data'
            
            # Use plan suggestion if available
            planned_type = visualization_plan.get('visualization_type')
            if planned_type:
                return planned_type
            
            first_row = query_results[0]
            columns = list(first_row.keys())
            
            # Analyze data structure
            numeric_columns = []
            text_columns = []
            
            for col in columns:
                sample_value = first_row.get(col)
                if isinstance(sample_value, (int, float)):
                    numeric_columns.append(col)
                else:
                    text_columns.append(col)
            
            # Decision logic
            if len(columns) == 2 and len(numeric_columns) == 1 and len(text_columns) == 1:
                # One category, one value - good for bar chart
                if len(query_results) <= 10:
                    return 'pie_chart'
                else:
                    return 'bar_chart'
            elif len(numeric_columns) >= 1 and 'date' in str(columns).lower():
                return 'line_chart'
            elif len(numeric_columns) >= 1:
                return 'bar_chart'
            else:
                return 'table'
                
        except Exception as e:
            logger.error(f"Error determining visualization type: {str(e)}")
            return 'table'
    
    def _create_bar_chart(self, query_results: List[Dict[str, Any]], 
                         visualization_plan: Dict[str, Any]) -> str:
        """Create a bar chart visualization"""
        try:
            first_row = query_results[0]
            columns = list(first_row.keys())
            
            # Find category and value columns
            category_col = None
            value_col = None
            
            for col in columns:
                sample_value = first_row.get(col)
                if isinstance(sample_value, (int, float)) and value_col is None:
                    value_col = col
                elif not isinstance(sample_value, (int, float)) and category_col is None:
                    category_col = col
            
            if not category_col or not value_col:
                return self._create_data_table(query_results, visualization_plan)
            
            # Prepare data
            chart_data = []
            for row in query_results[:20]:  # Limit to 20 items
                category = str(row.get(category_col, 'Unknown'))
                value = row.get(value_col, 0)
                if isinstance(value, (int, float)):
                    chart_data.append({'category': category, 'value': value})
            
            chart_title = visualization_plan.get('chart_title', 'Bar Chart')
            
            html = f"""
<div style="width: 100%; height: 400px; padding: 20px;">
    <h3 style="text-align: center; margin-bottom: 20px;">{chart_title}</h3>
    <canvas id="barChart" width="800" height="400"></canvas>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const ctx = document.getElementById('barChart').getContext('2d');
        const chart = new Chart(ctx, {{
            type: 'bar',
            data: {{
                labels: {json.dumps([item['category'] for item in chart_data])},
                datasets: [{{
                    label: '{value_col}',
                    data: {json.dumps([item['value'] for item in chart_data])},
                    backgroundColor: 'rgba(54, 162, 235, 0.6)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});
    </script>
</div>
"""
            return html
            
        except Exception as e:
            logger.error(f"Error creating bar chart: {str(e)}")
            return self._create_data_table(query_results, visualization_plan)
    
    def _create_line_chart(self, query_results: List[Dict[str, Any]], 
                          visualization_plan: Dict[str, Any]) -> str:
        """Create a line chart visualization"""
        try:
            first_row = query_results[0]
            columns = list(first_row.keys())
            
            # Find x and y columns
            x_col = columns[0]  # Assume first column is x-axis
            y_col = None
            
            for col in columns[1:]:
                sample_value = first_row.get(col)
                if isinstance(sample_value, (int, float)):
                    y_col = col
                    break
            
            if not y_col:
                return self._create_data_table(query_results, visualization_plan)
            
            # Prepare data
            chart_data = []
            for row in query_results:
                x_value = str(row.get(x_col, ''))
                y_value = row.get(y_col, 0)
                if isinstance(y_value, (int, float)):
                    chart_data.append({'x': x_value, 'y': y_value})
            
            chart_title = visualization_plan.get('chart_title', 'Line Chart')
            
            html = f"""
<div style="width: 100%; height: 400px; padding: 20px;">
    <h3 style="text-align: center; margin-bottom: 20px;">{chart_title}</h3>
    <canvas id="lineChart" width="800" height="400"></canvas>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const ctx = document.getElementById('lineChart').getContext('2d');
        const chart = new Chart(ctx, {{
            type: 'line',
            data: {{
                labels: {json.dumps([item['x'] for item in chart_data])},
                datasets: [{{
                    label: '{y_col}',
                    data: {json.dumps([item['y'] for item in chart_data])},
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }}]
            }},
            options: {{
                responsive: true,
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});
    </script>
</div>
"""
            return html
            
        except Exception as e:
            logger.error(f"Error creating line chart: {str(e)}")
            return self._create_data_table(query_results, visualization_plan)
    
    def _create_pie_chart(self, query_results: List[Dict[str, Any]], 
                         visualization_plan: Dict[str, Any]) -> str:
        """Create a pie chart visualization"""
        try:
            first_row = query_results[0]
            columns = list(first_row.keys())
            
            # Find label and value columns
            label_col = None
            value_col = None
            
            for col in columns:
                sample_value = first_row.get(col)
                if isinstance(sample_value, (int, float)) and value_col is None:
                    value_col = col
                elif not isinstance(sample_value, (int, float)) and label_col is None:
                    label_col = col
            
            if not label_col or not value_col:
                return self._create_data_table(query_results, visualization_plan)
            
            # Prepare data
            chart_data = []
            colors = [
                'rgba(255, 99, 132, 0.6)', 'rgba(54, 162, 235, 0.6)', 'rgba(255, 205, 86, 0.6)',
                'rgba(75, 192, 192, 0.6)', 'rgba(153, 102, 255, 0.6)', 'rgba(255, 159, 64, 0.6)',
                'rgba(199, 199, 199, 0.6)', 'rgba(83, 102, 255, 0.6)'
            ]
            
            for i, row in enumerate(query_results[:8]):  # Limit to 8 slices
                label = str(row.get(label_col, 'Unknown'))
                value = row.get(value_col, 0)
                if isinstance(value, (int, float)):
                    chart_data.append({
                        'label': label, 
                        'value': value, 
                        'color': colors[i % len(colors)]
                    })
            
            chart_title = visualization_plan.get('chart_title', 'Pie Chart')
            
            html = f"""
<div style="width: 100%; height: 400px; padding: 20px;">
    <h3 style="text-align: center; margin-bottom: 20px;">{chart_title}</h3>
    <canvas id="pieChart" width="400" height="400"></canvas>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        const ctx = document.getElementById('pieChart').getContext('2d');
        const chart = new Chart(ctx, {{
            type: 'pie',
            data: {{
                labels: {json.dumps([item['label'] for item in chart_data])},
                datasets: [{{
                    data: {json.dumps([item['value'] for item in chart_data])},
                    backgroundColor: {json.dumps([item['color'] for item in chart_data])}
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    legend: {{
                        position: 'bottom'
                    }}
                }}
            }}
        }});
    </script>
</div>
"""
            return html
            
        except Exception as e:
            logger.error(f"Error creating pie chart: {str(e)}")
            return self._create_data_table(query_results, visualization_plan)
    
    def _create_data_table(self, query_results: List[Dict[str, Any]], 
                          visualization_plan: Dict[str, Any]) -> str:
        """Create a data table visualization"""
        try:
            if not query_results:
                return self._create_no_data_visualization()
            
            columns = list(query_results[0].keys())
            chart_title = visualization_plan.get('chart_title', 'Data Table')
            
            # Create table HTML
            html = f"""
<div style="width: 100%; padding: 20px;">
    <h3 style="text-align: center; margin-bottom: 20px;">{chart_title}</h3>
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
            <thead>
                <tr style="background-color: #f2f2f2;">
"""
            
            # Add headers
            for col in columns:
                html += f'<th style="border: 1px solid #ddd; padding: 8px; text-align: left;">{col}</th>'
            
            html += """
                </tr>
            </thead>
            <tbody>
"""
            
            # Add data rows (limit to 50 rows)
            for i, row in enumerate(query_results[:50]):
                bg_color = '#f9f9f9' if i % 2 == 0 else 'white'
                html += f'<tr style="background-color: {bg_color};">'
                
                for col in columns:
                    value = row.get(col, '')
                    # Format numbers
                    if isinstance(value, float):
                        value = f"{value:.2f}"
                    elif value is None:
                        value = ''
                    
                    html += f'<td style="border: 1px solid #ddd; padding: 8px;">{value}</td>'
                
                html += '</tr>'
            
            html += """
            </tbody>
        </table>
    </div>
"""
            
            if len(query_results) > 50:
                html += f'<p style="text-align: center; margin-top: 10px; color: #666;">Showing first 50 of {len(query_results)} results</p>'
            
            html += '</div>'
            
            return html
            
        except Exception as e:
            logger.error(f"Error creating data table: {str(e)}")
            return self._create_error_visualization(str(e))
    
    def _create_default_visualization(self, query_results: List[Dict[str, Any]], 
                                    visualization_plan: Dict[str, Any]) -> str:
        """Create default visualization when type is unclear"""
        return self._create_data_table(query_results, visualization_plan)
    
    def _create_no_data_visualization(self) -> str:
        """Create visualization for when there's no data"""
        return """
<div style="width: 100%; height: 200px; padding: 20px; text-align: center; border: 2px dashed #ccc; border-radius: 8px;">
    <div style="margin-top: 60px;">
        <h3 style="color: #666;">No Data Available</h3>
        <p style="color: #999;">No results were found for your query.</p>
    </div>
</div>
"""
    
    def _create_error_visualization(self, error_message: str) -> str:
        """Create visualization for errors"""
        return f"""
<div style="width: 100%; height: 200px; padding: 20px; text-align: center; border: 2px solid #ff6b6b; border-radius: 8px; background-color: #ffe0e0;">
    <div style="margin-top: 60px;">
        <h3 style="color: #d63031;">Visualization Error</h3>
        <p style="color: #636e72;">Unable to create visualization: {error_message}</p>
    </div>
</div>
"""
