"""
Database Chat QA Agent

This agent validates answer completeness and quality.
It ensures responses adequately address user queries and meet quality standards.
"""

import logging
import traceback
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

from utils.openai_utils import openai_client
from config.settings import AZURE_OPENAI_DEPLOYMENT_NAME

logger = logging.getLogger(__name__)

class DatabaseChatQAAgent:
    """
    Quality assurance agent that validates answer completeness
    """
    
    def __init__(self):
        self.client = openai_client
        self.model = AZURE_OPENAI_DEPLOYMENT_NAME
        self.max_tokens = 1500
    
    def validate_response(self, user_query: str, generated_answer: str, 
                         query_results: List[Dict[str, Any]], 
                         execution_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate that the response adequately answers the user query
        
        Args:
            user_query: Original user question
            generated_answer: The generated natural language answer
            query_results: Raw query results
            execution_context: Context about query execution
            
        Returns:
            Dictionary containing quality assessment
        """
        try:
            logger.info(f"🔍 Validating response quality...")
            
            # Perform multiple quality checks
            completeness_check = self._check_answer_completeness(user_query, generated_answer, query_results)
            accuracy_check = self._check_answer_accuracy(generated_answer, query_results)
            clarity_check = self._check_answer_clarity(generated_answer)
            relevance_check = self._check_answer_relevance(user_query, generated_answer)
            
            # Calculate overall quality score
            overall_score = self._calculate_overall_quality_score(
                completeness_check, accuracy_check, clarity_check, relevance_check
            )
            
            # Generate improvement suggestions if needed
            suggestions = self._generate_improvement_suggestions(
                completeness_check, accuracy_check, clarity_check, relevance_check
            )
            
            # Determine if response needs improvement
            needs_improvement = overall_score < 0.7
            
            quality_assessment = {
                'overall_score': overall_score,
                'quality_grade': self._get_quality_grade(overall_score),
                'needs_improvement': needs_improvement,
                'completeness': completeness_check,
                'accuracy': accuracy_check,
                'clarity': clarity_check,
                'relevance': relevance_check,
                'improvement_suggestions': suggestions,
                'validation_timestamp': datetime.utcnow().isoformat()
            }
            
            logger.info(f"🔍 QA validation completed: {quality_assessment['quality_grade']} grade")
            return {
                'success': True,
                'quality_assessment': quality_assessment
            }
            
        except Exception as e:
            logger.error(f"Error validating response: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e)
            }
    
    def _check_answer_completeness(self, user_query: str, generated_answer: str, 
                                 query_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check if the answer completely addresses the user's question"""
        try:
            prompt = f"""
Evaluate if the generated answer completely addresses the user's question.

User Question: "{user_query}"
Generated Answer: "{generated_answer}"
Number of Results: {len(query_results)}

Rate the completeness on a scale of 0.0 to 1.0 and provide reasoning.

Return JSON:
{{
    "completeness_score": 0.8,
    "addresses_main_question": true,
    "missing_elements": ["list of missing elements"],
    "reasoning": "explanation of the score"
}}
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a quality assurance expert. Evaluate answer completeness and return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content.strip()
            result = json.loads(result_text)
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking answer completeness: {str(e)}")
            return {
                'completeness_score': 0.5,
                'addresses_main_question': True,
                'missing_elements': [],
                'reasoning': 'Error in completeness evaluation'
            }
    
    def _check_answer_accuracy(self, generated_answer: str, 
                             query_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check if the answer accurately reflects the query results"""
        try:
            # Prepare results summary for comparison
            results_summary = self._prepare_results_summary(query_results)
            
            prompt = f"""
Evaluate if the generated answer accurately reflects the query results.

Generated Answer: "{generated_answer}"
Query Results Summary: {results_summary}

Check for:
1. Correct numbers and statistics
2. Accurate data interpretation
3. No contradictions with the data
4. Proper representation of findings

Return JSON:
{{
    "accuracy_score": 0.9,
    "factual_errors": ["list of any factual errors"],
    "data_misrepresentations": ["list of misrepresentations"],
    "reasoning": "explanation of the score"
}}
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a data accuracy expert. Evaluate answer accuracy and return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content.strip()
            result = json.loads(result_text)
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking answer accuracy: {str(e)}")
            return {
                'accuracy_score': 0.7,
                'factual_errors': [],
                'data_misrepresentations': [],
                'reasoning': 'Error in accuracy evaluation'
            }
    
    def _check_answer_clarity(self, generated_answer: str) -> Dict[str, Any]:
        """Check if the answer is clear and well-structured"""
        try:
            clarity_metrics = {
                'clarity_score': 0.8,
                'readability_issues': [],
                'structure_issues': [],
                'language_issues': [],
                'reasoning': 'Answer clarity assessment'
            }
            
            # Basic clarity checks
            answer_length = len(generated_answer)
            sentence_count = generated_answer.count('.') + generated_answer.count('!') + generated_answer.count('?')
            
            # Check for overly long or short answers
            if answer_length < 50:
                clarity_metrics['structure_issues'].append("Answer is very short and may lack detail")
                clarity_metrics['clarity_score'] -= 0.2
            elif answer_length > 1000:
                clarity_metrics['structure_issues'].append("Answer is very long and may be overwhelming")
                clarity_metrics['clarity_score'] -= 0.1
            
            # Check for sentence structure
            if sentence_count == 0:
                clarity_metrics['structure_issues'].append("No clear sentence structure")
                clarity_metrics['clarity_score'] -= 0.3
            elif answer_length / sentence_count > 200:  # Very long sentences
                clarity_metrics['readability_issues'].append("Sentences are too long")
                clarity_metrics['clarity_score'] -= 0.1
            
            # Check for technical jargon
            technical_terms = ['SQL', 'database', 'query', 'table', 'column', 'JOIN', 'WHERE']
            tech_count = sum(1 for term in technical_terms if term in generated_answer)
            if tech_count > 3:
                clarity_metrics['language_issues'].append("Contains too much technical jargon")
                clarity_metrics['clarity_score'] -= 0.1
            
            clarity_metrics['clarity_score'] = max(0.0, clarity_metrics['clarity_score'])
            
            return clarity_metrics
            
        except Exception as e:
            logger.error(f"Error checking answer clarity: {str(e)}")
            return {
                'clarity_score': 0.6,
                'readability_issues': [],
                'structure_issues': [],
                'language_issues': [],
                'reasoning': 'Error in clarity evaluation'
            }
    
    def _check_answer_relevance(self, user_query: str, generated_answer: str) -> Dict[str, Any]:
        """Check if the answer is relevant to the user's question"""
        try:
            prompt = f"""
Evaluate if the generated answer is relevant to the user's question.

User Question: "{user_query}"
Generated Answer: "{generated_answer}"

Check for:
1. Direct relevance to the question asked
2. Appropriate level of detail
3. Focus on what the user actually wanted to know
4. No unnecessary tangents

Return JSON:
{{
    "relevance_score": 0.9,
    "stays_on_topic": true,
    "addresses_intent": true,
    "unnecessary_information": ["list of irrelevant parts"],
    "reasoning": "explanation of the score"
}}
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a relevance expert. Evaluate answer relevance and return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=600,
                temperature=0.1
            )
            
            result_text = response.choices[0].message.content.strip()
            result = json.loads(result_text)
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking answer relevance: {str(e)}")
            return {
                'relevance_score': 0.7,
                'stays_on_topic': True,
                'addresses_intent': True,
                'unnecessary_information': [],
                'reasoning': 'Error in relevance evaluation'
            }
    
    def _prepare_results_summary(self, query_results: List[Dict[str, Any]]) -> str:
        """Prepare a summary of query results for accuracy checking"""
        try:
            if not query_results:
                return "No results returned"
            
            summary_parts = [f"Total results: {len(query_results)}"]
            
            if query_results:
                first_row = query_results[0]
                columns = list(first_row.keys())
                summary_parts.append(f"Columns: {', '.join(columns)}")
                
                # Add sample values
                if len(query_results) <= 3:
                    summary_parts.append("All results:")
                    for i, row in enumerate(query_results):
                        row_summary = ", ".join([f"{k}: {v}" for k, v in row.items()])
                        summary_parts.append(f"  Row {i+1}: {row_summary}")
                else:
                    summary_parts.append("Sample results:")
                    for i, row in enumerate(query_results[:2]):
                        row_summary = ", ".join([f"{k}: {v}" for k, v in row.items()])
                        summary_parts.append(f"  Row {i+1}: {row_summary}")
                    summary_parts.append(f"  ... and {len(query_results) - 2} more rows")
            
            return "\n".join(summary_parts)
            
        except Exception as e:
            logger.error(f"Error preparing results summary: {str(e)}")
            return "Error preparing results summary"
    
    def _calculate_overall_quality_score(self, completeness: Dict[str, Any], 
                                       accuracy: Dict[str, Any],
                                       clarity: Dict[str, Any], 
                                       relevance: Dict[str, Any]) -> float:
        """Calculate overall quality score from individual metrics"""
        try:
            # Weighted average of quality metrics
            weights = {
                'completeness': 0.3,
                'accuracy': 0.3,
                'relevance': 0.25,
                'clarity': 0.15
            }
            
            scores = {
                'completeness': completeness.get('completeness_score', 0.5),
                'accuracy': accuracy.get('accuracy_score', 0.5),
                'relevance': relevance.get('relevance_score', 0.5),
                'clarity': clarity.get('clarity_score', 0.5)
            }
            
            overall_score = sum(weights[metric] * scores[metric] for metric in weights)
            
            return round(overall_score, 2)
            
        except Exception as e:
            logger.error(f"Error calculating overall quality score: {str(e)}")
            return 0.5
    
    def _get_quality_grade(self, score: float) -> str:
        """Convert quality score to letter grade"""
        if score >= 0.9:
            return 'A'
        elif score >= 0.8:
            return 'B'
        elif score >= 0.7:
            return 'C'
        elif score >= 0.6:
            return 'D'
        else:
            return 'F'
    
    def _generate_improvement_suggestions(self, completeness: Dict[str, Any], 
                                        accuracy: Dict[str, Any],
                                        clarity: Dict[str, Any], 
                                        relevance: Dict[str, Any]) -> List[str]:
        """Generate suggestions for improving the answer"""
        suggestions = []
        
        try:
            # Completeness suggestions
            if completeness.get('completeness_score', 1.0) < 0.7:
                missing = completeness.get('missing_elements', [])
                if missing:
                    suggestions.append(f"Address missing elements: {', '.join(missing)}")
                else:
                    suggestions.append("Provide more complete information to fully answer the question")
            
            # Accuracy suggestions
            if accuracy.get('accuracy_score', 1.0) < 0.8:
                errors = accuracy.get('factual_errors', [])
                if errors:
                    suggestions.append(f"Correct factual errors: {', '.join(errors)}")
                misreps = accuracy.get('data_misrepresentations', [])
                if misreps:
                    suggestions.append(f"Fix data misrepresentations: {', '.join(misreps)}")
            
            # Clarity suggestions
            if clarity.get('clarity_score', 1.0) < 0.7:
                structure_issues = clarity.get('structure_issues', [])
                readability_issues = clarity.get('readability_issues', [])
                if structure_issues:
                    suggestions.append("Improve answer structure and organization")
                if readability_issues:
                    suggestions.append("Improve readability and sentence structure")
            
            # Relevance suggestions
            if relevance.get('relevance_score', 1.0) < 0.8:
                if not relevance.get('addresses_intent', True):
                    suggestions.append("Better address the user's actual intent")
                unnecessary = relevance.get('unnecessary_information', [])
                if unnecessary:
                    suggestions.append("Remove unnecessary or off-topic information")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating improvement suggestions: {str(e)}")
            return ["Review and improve answer quality"]
