"""
Database Chat Planner Agent

This agent analyzes user queries and creates execution plans for the agentic chat system.
It determines what type of response is needed, which tables to query, and what visualizations
might be helpful.
"""

import logging
import traceback
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from utils.openai_utils import openai_client
from config.settings import AZURE_OPENAI_DEPLOYMENT_NAME

logger = logging.getLogger(__name__)

class DatabaseChatPlannerAgent:
    """
    Planner agent that analyzes user queries and creates execution plans
    """
    
    def __init__(self):
        self.client = openai_client
        self.model = AZURE_OPENAI_DEPLOYMENT_NAME
        self.max_tokens = 2000
    
    def create_execution_plan(self, user_query: str, role_schema: Dict[str, Any], 
                            semantic_summaries: Dict[str, Any], 
                            chat_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create an execution plan for a user query
        
        Args:
            user_query: The user's natural language query
            role_schema: The filtered schema for the user's role
            semantic_summaries: Semantic summaries for context
            chat_history: Previous messages in the conversation
            
        Returns:
            Dictionary containing the execution plan
        """
        try:
            logger.info(f"🤖 Creating execution plan for query: {user_query[:100]}...")
            
            # Analyze query intent
            query_analysis = self._analyze_query_intent(user_query)
            
            # Determine required tables and columns
            table_analysis = self._analyze_required_tables(
                user_query, role_schema, semantic_summaries
            )
            
            # Determine visualization requirements
            visualization_plan = self._plan_visualization(user_query, query_analysis)
            
            # Create the execution plan
            execution_plan = {
                'query_analysis': query_analysis,
                'table_analysis': table_analysis,
                'visualization_plan': visualization_plan,
                'execution_steps': self._create_execution_steps(
                    query_analysis, table_analysis, visualization_plan
                ),
                'complexity_score': self._calculate_complexity_score(
                    query_analysis, table_analysis
                ),
                'estimated_processing_time': self._estimate_processing_time(
                    query_analysis, table_analysis
                ),
                'created_at': datetime.utcnow().isoformat()
            }
            
            logger.info(f"🤖 Execution plan created with {len(execution_plan['execution_steps'])} steps")
            return {
                'success': True,
                'plan': execution_plan
            }
            
        except Exception as e:
            logger.error(f"Error creating execution plan: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e)
            }
    
    def _analyze_query_intent(self, user_query: str) -> Dict[str, Any]:
        """Analyze the intent and type of the user query"""
        try:
            prompt = f"""
Analyze the following user query and determine its intent and characteristics.

User Query: "{user_query}"

Return a JSON object with the following structure:
{{
    "intent_type": "data_retrieval|aggregation|comparison|trend_analysis|filtering|reporting",
    "query_complexity": "simple|medium|complex",
    "requires_aggregation": true/false,
    "requires_joins": true/false,
    "requires_filtering": true/false,
    "requires_sorting": true/false,
    "requires_grouping": true/false,
    "time_dimension": true/false,
    "numerical_analysis": true/false,
    "keywords": ["list", "of", "key", "terms"],
    "entities": ["specific", "entities", "mentioned"],
    "question_type": "what|how|when|where|why|who|count|sum|average|list",
    "expected_result_type": "single_value|list|table|summary|chart"
}}
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at analyzing database queries. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=800,
                temperature=0.1
            )
            
            analysis_text = response.choices[0].message.content.strip()
            analysis = json.loads(analysis_text)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing query intent: {str(e)}")
            return {
                'intent_type': 'data_retrieval',
                'query_complexity': 'medium',
                'requires_aggregation': False,
                'requires_joins': False,
                'requires_filtering': True,
                'requires_sorting': False,
                'requires_grouping': False,
                'time_dimension': False,
                'numerical_analysis': False,
                'keywords': [],
                'entities': [],
                'question_type': 'what',
                'expected_result_type': 'table'
            }
    
    def _analyze_required_tables(self, user_query: str, role_schema: Dict[str, Any], 
                               semantic_summaries: Dict[str, Any]) -> Dict[str, Any]:
        """Determine which tables and columns are needed for the query"""
        try:
            # Prepare context about available tables
            tables_context = self._prepare_tables_context(role_schema, semantic_summaries)
            
            prompt = f"""
Based on the user query and available database schema, determine which tables and columns are needed.

User Query: "{user_query}"

Available Tables and Context:
{tables_context}

Return a JSON object with:
{{
    "primary_tables": ["main tables needed"],
    "secondary_tables": ["tables that might be joined"],
    "required_columns": {{
        "table_name": ["column1", "column2"]
    }},
    "join_relationships": [
        {{
            "from_table": "table1",
            "to_table": "table2", 
            "join_type": "INNER|LEFT|RIGHT",
            "join_condition": "table1.id = table2.foreign_id"
        }}
    ],
    "filter_conditions": [
        {{
            "table": "table_name",
            "column": "column_name",
            "operator": "=|>|<|LIKE|IN",
            "value_type": "string|number|date|list"
        }}
    ],
    "confidence_score": 0.8
}}
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a database expert. Analyze queries and return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1200,
                temperature=0.1
            )
            
            analysis_text = response.choices[0].message.content.strip()
            analysis = json.loads(analysis_text)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing required tables: {str(e)}")
            return {
                'primary_tables': [],
                'secondary_tables': [],
                'required_columns': {},
                'join_relationships': [],
                'filter_conditions': [],
                'confidence_score': 0.5
            }
    
    def _prepare_tables_context(self, role_schema: Dict[str, Any], 
                              semantic_summaries: Dict[str, Any]) -> str:
        """Prepare context about available tables for the LLM"""
        try:
            context_parts = []
            
            tables = role_schema.get('tables', {})
            
            for table_name, table_info in tables.items():
                context_parts.append(f"\nTable: {table_name}")
                context_parts.append(f"Type: {table_info.get('type', 'table')}")
                
                # Add semantic summary if available
                table_summary_key = f"table_{table_name}"
                if table_summary_key in semantic_summaries:
                    summary = semantic_summaries[table_summary_key]
                    context_parts.append(f"Description: {summary.get('summary', '')[:200]}")
                
                # Add column information
                columns = table_info.get('columns', [])
                if columns:
                    context_parts.append("Columns:")
                    for col in columns[:10]:  # Limit to first 10 columns
                        col_desc = f"  - {col['name']} ({col['type']})"
                        if col.get('primary_key'):
                            col_desc += " [PK]"
                        if not col.get('nullable', True):
                            col_desc += " [NOT NULL]"
                        context_parts.append(col_desc)
                    
                    if len(columns) > 10:
                        context_parts.append(f"  ... and {len(columns) - 10} more columns")
                
                context_parts.append("")  # Empty line between tables
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error preparing tables context: {str(e)}")
            return "No table information available"
    
    def _plan_visualization(self, user_query: str, query_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Determine if and what type of visualization is needed"""
        try:
            # Check if visualization keywords are present
            viz_keywords = [
                'chart', 'graph', 'plot', 'visualize', 'show', 'trend', 'compare',
                'distribution', 'breakdown', 'over time', 'by month', 'by year'
            ]
            
            query_lower = user_query.lower()
            has_viz_keywords = any(keyword in query_lower for keyword in viz_keywords)
            
            # Determine visualization type based on query analysis
            viz_type = None
            if query_analysis.get('time_dimension'):
                viz_type = 'line_chart'
            elif query_analysis.get('requires_grouping'):
                viz_type = 'bar_chart'
            elif query_analysis.get('numerical_analysis'):
                if 'distribution' in query_lower:
                    viz_type = 'histogram'
                else:
                    viz_type = 'bar_chart'
            elif has_viz_keywords:
                viz_type = 'bar_chart'  # Default visualization
            
            return {
                'requires_visualization': bool(viz_type or has_viz_keywords),
                'visualization_type': viz_type,
                'chart_title': self._generate_chart_title(user_query),
                'x_axis_label': None,  # Will be determined by SQL agent
                'y_axis_label': None,  # Will be determined by SQL agent
                'confidence': 0.8 if viz_type else 0.3
            }
            
        except Exception as e:
            logger.error(f"Error planning visualization: {str(e)}")
            return {
                'requires_visualization': False,
                'visualization_type': None,
                'chart_title': None,
                'confidence': 0.0
            }
    
    def _generate_chart_title(self, user_query: str) -> str:
        """Generate an appropriate chart title from the user query"""
        try:
            # Simple title generation - could be improved with LLM
            query_words = user_query.split()
            if len(query_words) <= 8:
                return user_query.title()
            else:
                return " ".join(query_words[:8]).title() + "..."
                
        except Exception as e:
            logger.error(f"Error generating chart title: {str(e)}")
            return "Data Visualization"
    
    def _create_execution_steps(self, query_analysis: Dict[str, Any], 
                              table_analysis: Dict[str, Any],
                              visualization_plan: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create ordered execution steps for the query"""
        try:
            steps = []
            
            # Step 1: SQL Generation
            steps.append({
                'step_number': 1,
                'agent': 'sql_agent',
                'action': 'generate_sql',
                'description': 'Generate SQL query based on user request and schema',
                'inputs': {
                    'query_analysis': query_analysis,
                    'table_analysis': table_analysis
                },
                'expected_output': 'sql_query'
            })
            
            # Step 2: SQL Validation
            steps.append({
                'step_number': 2,
                'agent': 'validator_agent',
                'action': 'validate_sql',
                'description': 'Validate SQL query against role permissions and restrictions',
                'inputs': {
                    'sql_query': 'from_previous_step'
                },
                'expected_output': 'validated_sql_query'
            })
            
            # Step 3: Query Execution
            steps.append({
                'step_number': 3,
                'agent': 'sql_executor',
                'action': 'execute_query',
                'description': 'Execute validated SQL query against database',
                'inputs': {
                    'sql_query': 'from_previous_step'
                },
                'expected_output': 'query_results'
            })
            
            # Step 4: Answer Generation
            steps.append({
                'step_number': 4,
                'agent': 'answer_maker_agent',
                'action': 'generate_answer',
                'description': 'Generate natural language answer from query results',
                'inputs': {
                    'query_results': 'from_previous_step',
                    'original_query': 'user_query'
                },
                'expected_output': 'natural_language_answer'
            })
            
            # Step 5: Visualization (if needed)
            if visualization_plan.get('requires_visualization'):
                steps.append({
                    'step_number': 5,
                    'agent': 'graph_agent',
                    'action': 'create_visualization',
                    'description': 'Create data visualization from query results',
                    'inputs': {
                        'query_results': 'from_step_3',
                        'visualization_plan': visualization_plan
                    },
                    'expected_output': 'visualization_html'
                })
            
            # Step 6: Quality Assurance
            steps.append({
                'step_number': len(steps) + 1,
                'agent': 'qa_agent',
                'action': 'validate_response',
                'description': 'Validate that the response adequately answers the user query',
                'inputs': {
                    'original_query': 'user_query',
                    'generated_answer': 'from_answer_maker',
                    'query_results': 'from_step_3'
                },
                'expected_output': 'quality_assessment'
            })
            
            return steps
            
        except Exception as e:
            logger.error(f"Error creating execution steps: {str(e)}")
            return []
    
    def _calculate_complexity_score(self, query_analysis: Dict[str, Any], 
                                  table_analysis: Dict[str, Any]) -> float:
        """Calculate a complexity score for the query (0.0 to 1.0)"""
        try:
            score = 0.0
            
            # Base complexity from query analysis
            complexity_map = {
                'simple': 0.2,
                'medium': 0.5,
                'complex': 0.8
            }
            score += complexity_map.get(query_analysis.get('query_complexity', 'medium'), 0.5)
            
            # Add complexity for various features
            if query_analysis.get('requires_joins'):
                score += 0.2
            if query_analysis.get('requires_aggregation'):
                score += 0.1
            if query_analysis.get('requires_grouping'):
                score += 0.1
            if query_analysis.get('time_dimension'):
                score += 0.1
            
            # Add complexity based on number of tables
            num_tables = len(table_analysis.get('primary_tables', [])) + len(table_analysis.get('secondary_tables', []))
            if num_tables > 2:
                score += 0.1 * (num_tables - 2)
            
            # Cap at 1.0
            return min(score, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating complexity score: {str(e)}")
            return 0.5
    
    def _estimate_processing_time(self, query_analysis: Dict[str, Any], 
                                table_analysis: Dict[str, Any]) -> int:
        """Estimate processing time in milliseconds"""
        try:
            base_time = 1000  # 1 second base
            
            # Add time based on complexity
            if query_analysis.get('query_complexity') == 'complex':
                base_time += 2000
            elif query_analysis.get('query_complexity') == 'medium':
                base_time += 1000
            
            # Add time for joins
            if query_analysis.get('requires_joins'):
                base_time += 1500
            
            # Add time for aggregations
            if query_analysis.get('requires_aggregation'):
                base_time += 500
            
            # Add time for multiple tables
            num_tables = len(table_analysis.get('primary_tables', [])) + len(table_analysis.get('secondary_tables', []))
            base_time += (num_tables - 1) * 500
            
            return base_time
            
        except Exception as e:
            logger.error(f"Error estimating processing time: {str(e)}")
            return 3000  # Default 3 seconds
