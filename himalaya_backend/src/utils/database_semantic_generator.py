"""
Database Semantic Summary Generator

This module generates LLM-powered semantic summaries for databases, tables, and columns
to help with natural language query generation and understanding.
"""

import logging
import traceback
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from utils.openai_utils import get_openai_client
from config.settings import OPENAI_API_KEY, OPENAI_MODEL

logger = logging.getLogger(__name__)

class DatabaseSemanticGenerator:
    """
    Generates semantic summaries for database components using LLM
    """
    
    def __init__(self):
        self.client = get_openai_client()
        self.model = OPENAI_MODEL
        self.max_tokens = 2000  # Keep summaries concise for context management
    
    def generate_database_summary(self, db_config: Dict[str, Any], schema_analysis: Dict[str, Any], 
                                eda_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate comprehensive semantic summary for entire database
        
        Args:
            db_config: Database configuration
            schema_analysis: Schema analysis results
            eda_results: EDA analysis results
            
        Returns:
            Dictionary containing semantic summary
        """
        try:
            logger.info(f"Generating semantic summary for database: {db_config.get('name', 'Unknown')}")
            
            # Prepare context for LLM
            context = self._prepare_database_context(db_config, schema_analysis, eda_results)
            
            # Generate summary using LLM
            summary = self._generate_llm_summary(context, 'database')
            
            return {
                'success': True,
                'summary_type': 'database',
                'target_name': db_config.get('name', 'Unknown'),
                'llm_generated_summary': summary,
                'current_summary': summary,
                'token_count': len(summary.split()),
                'generated_at': datetime.utcnow().isoformat(),
                'context_used': {
                    'total_tables': len(schema_analysis.get('tables', {})),
                    'total_columns': schema_analysis.get('total_columns', 0),
                    'eda_available': bool(eda_results.get('success', False))
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating database summary: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'generated_at': datetime.utcnow().isoformat()
            }
    
    def generate_table_summaries(self, db_config: Dict[str, Any], schema_analysis: Dict[str, Any], 
                               eda_results: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Generate semantic summaries for all tables in the database
        
        Args:
            db_config: Database configuration
            schema_analysis: Schema analysis results
            eda_results: EDA analysis results
            
        Returns:
            Dictionary mapping table names to their semantic summaries
        """
        try:
            logger.info("Generating table summaries")
            table_summaries = {}
            
            tables_info = schema_analysis.get('tables', {})
            eda_table_results = eda_results.get('table_analyses', {})
            
            for table_name, table_info in tables_info.items():
                try:
                    logger.info(f"Generating summary for table: {table_name}")
                    
                    # Prepare table-specific context
                    table_context = self._prepare_table_context(
                        table_name, table_info, eda_table_results.get(table_name, {}), db_config
                    )
                    
                    # Generate summary
                    summary = self._generate_llm_summary(table_context, 'table')
                    
                    table_summaries[table_name] = {
                        'success': True,
                        'summary_type': 'table',
                        'target_name': table_name,
                        'llm_generated_summary': summary,
                        'current_summary': summary,
                        'token_count': len(summary.split()),
                        'generated_at': datetime.utcnow().isoformat(),
                        'context_used': {
                            'column_count': len(table_info.get('columns', [])),
                            'has_eda': table_name in eda_table_results,
                            'table_type': table_info.get('type', 'table')
                        }
                    }
                    
                except Exception as e:
                    logger.error(f"Error generating summary for table {table_name}: {str(e)}")
                    table_summaries[table_name] = {
                        'success': False,
                        'error': str(e),
                        'generated_at': datetime.utcnow().isoformat()
                    }
            
            logger.info(f"Generated summaries for {len(table_summaries)} tables")
            return table_summaries
            
        except Exception as e:
            logger.error(f"Error generating table summaries: {str(e)}")
            return {}
    
    def generate_column_summaries(self, table_name: str, table_info: Dict[str, Any], 
                                table_eda: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Generate semantic summaries for columns in a table
        
        Args:
            table_name: Name of the table
            table_info: Table schema information
            table_eda: Table EDA results
            
        Returns:
            Dictionary mapping column names to their semantic summaries
        """
        try:
            logger.info(f"Generating column summaries for table: {table_name}")
            column_summaries = {}
            
            columns = table_info.get('columns', [])
            column_analysis = table_eda.get('column_analysis', {})
            
            for column in columns:
                column_name = column['name']
                try:
                    # Prepare column-specific context
                    column_context = self._prepare_column_context(
                        table_name, column, column_analysis.get(column_name, {})
                    )
                    
                    # Generate summary
                    summary = self._generate_llm_summary(column_context, 'column')
                    
                    column_summaries[column_name] = {
                        'success': True,
                        'summary_type': 'column',
                        'target_name': column_name,
                        'llm_generated_summary': summary,
                        'current_summary': summary,
                        'token_count': len(summary.split()),
                        'generated_at': datetime.utcnow().isoformat(),
                        'context_used': {
                            'data_type': column.get('type'),
                            'nullable': column.get('nullable'),
                            'primary_key': column.get('primary_key'),
                            'has_analysis': column_name in column_analysis
                        }
                    }
                    
                except Exception as e:
                    logger.error(f"Error generating summary for column {column_name}: {str(e)}")
                    column_summaries[column_name] = {
                        'success': False,
                        'error': str(e),
                        'generated_at': datetime.utcnow().isoformat()
                    }
            
            logger.info(f"Generated summaries for {len(column_summaries)} columns")
            return column_summaries
            
        except Exception as e:
            logger.error(f"Error generating column summaries: {str(e)}")
            return {}
    
    def _prepare_database_context(self, db_config: Dict[str, Any], schema_analysis: Dict[str, Any], 
                                eda_results: Dict[str, Any]) -> str:
        """Prepare context for database-level summary generation"""
        try:
            context_parts = [
                f"Database Name: {db_config.get('name', 'Unknown')}",
                f"Database Type: {db_config.get('db_type', 'Unknown')}",
                f"Total Tables: {len(schema_analysis.get('tables', {}))}",
                f"Total Columns: {schema_analysis.get('total_columns', 0)}"
            ]
            
            # Add database info if available
            db_info = schema_analysis.get('database_info', {})
            if db_info.get('version'):
                context_parts.append(f"Database Version: {db_info['version']}")
            
            # Add table overview
            tables_info = schema_analysis.get('tables', {})
            if tables_info:
                context_parts.append("\nTable Overview:")
                for table_name, table_info in list(tables_info.items())[:10]:  # Limit to first 10 tables
                    context_parts.append(f"- {table_name}: {len(table_info.get('columns', []))} columns, "
                                       f"Type: {table_info.get('type', 'table')}")
                
                if len(tables_info) > 10:
                    context_parts.append(f"... and {len(tables_info) - 10} more tables")
            
            # Add EDA summary if available
            if eda_results.get('success'):
                db_summary = eda_results.get('database_summary', {})
                context_parts.append(f"\nData Quality Overview:")
                context_parts.append(f"- Total Rows: {db_summary.get('total_rows', 'Unknown')}")
                context_parts.append(f"- Average Missing Data: {db_summary.get('average_missing_percentage', 0):.1f}%")
                
                problematic_tables = db_summary.get('problematic_tables', [])
                if problematic_tables:
                    context_parts.append(f"- Tables with Quality Issues: {len(problematic_tables)}")
            
            # Add relationships if available
            relationships = schema_analysis.get('relationships', [])
            if relationships:
                context_parts.append(f"\nRelationships: {len(relationships)} foreign key relationships found")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error preparing database context: {str(e)}")
            return f"Database: {db_config.get('name', 'Unknown')}"
    
    def _prepare_table_context(self, table_name: str, table_info: Dict[str, Any], 
                             table_eda: Dict[str, Any], db_config: Dict[str, Any]) -> str:
        """Prepare context for table-level summary generation"""
        try:
            context_parts = [
                f"Table Name: {table_name}",
                f"Database: {db_config.get('name', 'Unknown')}",
                f"Table Type: {table_info.get('type', 'table')}",
                f"Column Count: {len(table_info.get('columns', []))}"
            ]
            
            # Add row count if available
            stats = table_info.get('statistics', {})
            if stats.get('row_count') is not None:
                context_parts.append(f"Row Count: {stats['row_count']:,}")
            
            # Add column information
            columns = table_info.get('columns', [])
            if columns:
                context_parts.append("\nColumns:")
                for col in columns[:15]:  # Limit to first 15 columns
                    col_desc = f"- {col['name']} ({col['type']})"
                    if col.get('primary_key'):
                        col_desc += " [PRIMARY KEY]"
                    if not col.get('nullable', True):
                        col_desc += " [NOT NULL]"
                    context_parts.append(col_desc)
                
                if len(columns) > 15:
                    context_parts.append(f"... and {len(columns) - 15} more columns")
            
            # Add primary keys
            primary_keys = table_info.get('primary_keys', [])
            if primary_keys:
                context_parts.append(f"\nPrimary Keys: {', '.join(primary_keys)}")
            
            # Add foreign keys
            foreign_keys = table_info.get('foreign_keys', [])
            if foreign_keys:
                context_parts.append(f"\nForeign Keys: {len(foreign_keys)} relationships")
                for fk in foreign_keys[:3]:  # Show first 3 FKs
                    ref_table = fk.get('referred_table', 'Unknown')
                    context_parts.append(f"- References {ref_table}")
            
            # Add EDA insights if available
            if table_eda.get('success'):
                context_parts.append(f"\nData Quality:")
                data_quality = table_eda.get('data_quality', {})
                context_parts.append(f"- Missing Data: {data_quality.get('missing_percentage', 0):.1f}%")
                context_parts.append(f"- Duplicate Rows: {data_quality.get('duplicate_percentage', 0):.1f}%")
                
                # Add interesting patterns
                basic_stats = table_eda.get('basic_stats', {})
                if basic_stats.get('numeric_columns'):
                    context_parts.append(f"- Numeric Columns: {len(basic_stats['numeric_columns'])}")
                if basic_stats.get('text_columns'):
                    context_parts.append(f"- Text Columns: {len(basic_stats['text_columns'])}")
                if basic_stats.get('date_columns'):
                    context_parts.append(f"- Date Columns: {len(basic_stats['date_columns'])}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error preparing table context: {str(e)}")
            return f"Table: {table_name}"
    
    def _prepare_column_context(self, table_name: str, column_info: Dict[str, Any], 
                              column_analysis: Dict[str, Any]) -> str:
        """Prepare context for column-level summary generation"""
        try:
            column_name = column_info['name']
            context_parts = [
                f"Column Name: {column_name}",
                f"Table: {table_name}",
                f"Data Type: {column_info.get('type', 'Unknown')}",
                f"Nullable: {'Yes' if column_info.get('nullable', True) else 'No'}"
            ]
            
            # Add column properties
            if column_info.get('primary_key'):
                context_parts.append("Role: Primary Key")
            if column_info.get('autoincrement'):
                context_parts.append("Auto-increment: Yes")
            if column_info.get('default') is not None:
                context_parts.append(f"Default Value: {column_info['default']}")
            
            # Add analysis results if available
            if column_analysis:
                context_parts.append(f"\nData Analysis:")
                context_parts.append(f"- Unique Values: {column_analysis.get('unique_count', 'Unknown')}")
                context_parts.append(f"- Null Percentage: {column_analysis.get('null_percentage', 0):.1f}%")
                
                # Type-specific analysis
                if 'min' in column_analysis:  # Numeric column
                    context_parts.append(f"- Range: {column_analysis.get('min')} to {column_analysis.get('max')}")
                    context_parts.append(f"- Mean: {column_analysis.get('mean', 'N/A')}")
                
                if 'avg_length' in column_analysis:  # Text column
                    context_parts.append(f"- Average Length: {column_analysis.get('avg_length', 0):.1f} characters")
                    most_common = column_analysis.get('most_common_values', {})
                    if most_common:
                        top_value = list(most_common.keys())[0]
                        context_parts.append(f"- Most Common Value: '{top_value}'")
                
                if 'min_date' in column_analysis:  # Date column
                    context_parts.append(f"- Date Range: {column_analysis.get('min_date')} to {column_analysis.get('max_date')}")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            logger.error(f"Error preparing column context: {str(e)}")
            return f"Column: {column_info.get('name', 'Unknown')}"
    
    def _generate_llm_summary(self, context: str, summary_type: str) -> str:
        """Generate semantic summary using LLM"""
        try:
            # Create appropriate prompt based on summary type
            if summary_type == 'database':
                prompt = self._get_database_prompt(context)
            elif summary_type == 'table':
                prompt = self._get_table_prompt(context)
            elif summary_type == 'column':
                prompt = self._get_column_prompt(context)
            else:
                raise ValueError(f"Unknown summary type: {summary_type}")
            
            # Call OpenAI API
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a database expert who creates concise, informative semantic summaries for SQL query generation."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.3  # Lower temperature for more consistent summaries
            )
            
            summary = response.choices[0].message.content.strip()
            
            # Validate summary length (keep under 2000 tokens for context management)
            if len(summary.split()) > 1800:
                logger.warning(f"Summary too long ({len(summary.split())} words), truncating")
                words = summary.split()[:1800]
                summary = ' '.join(words) + "..."
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating LLM summary: {str(e)}")
            return f"Error generating summary: {str(e)}"
    
    def _get_database_prompt(self, context: str) -> str:
        """Get prompt for database-level summary"""
        return f"""
Based on the following database information, create a comprehensive semantic summary that will help an AI system understand this database for natural language query generation.

{context}

Please provide a summary that includes:
1. The purpose and domain of this database (infer from table names and structure)
2. Key business entities and their relationships
3. Data quality and completeness overview
4. Important patterns or characteristics
5. Guidance for query generation

Keep the summary concise but informative (max 300 words). Focus on information that would help generate accurate SQL queries from natural language questions.
"""
    
    def _get_table_prompt(self, context: str) -> str:
        """Get prompt for table-level summary"""
        return f"""
Based on the following table information, create a semantic summary that will help an AI system understand this table for natural language query generation.

{context}

Please provide a summary that includes:
1. The business purpose of this table (what entity/concept it represents)
2. Key columns and their business meaning
3. Relationships to other tables
4. Data patterns and quality insights
5. Common query patterns this table might be used for

Keep the summary concise but informative (max 200 words). Focus on business context and query generation guidance.
"""
    
    def _get_column_prompt(self, context: str) -> str:
        """Get prompt for column-level summary"""
        return f"""
Based on the following column information, create a semantic summary that will help an AI system understand this column for natural language query generation.

{context}

Please provide a summary that includes:
1. The business meaning of this column
2. What kind of data it contains and typical values
3. How it might be used in queries (filtering, grouping, etc.)
4. Any important constraints or patterns

Keep the summary concise (max 100 words). Focus on practical query generation guidance.
"""
