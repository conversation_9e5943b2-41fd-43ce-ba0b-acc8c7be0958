"""
Database Column Masking Engine

This module handles column masking and data filtering for role-based access control.
It applies masking rules to query results and ensures data privacy compliance.
"""

import logging
import traceback
import hashlib
import re
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import pandas as pd

logger = logging.getLogger(__name__)

class DatabaseColumnMaskingEngine:
    """
    Handles column masking and data filtering based on role permissions
    """
    
    def __init__(self):
        self.masking_functions = {
            'mask': self._mask_value,
            'hash': self._hash_value,
            'hide': self._hide_value,
            'partial_mask': self._partial_mask_value,
            'email_mask': self._mask_email,
            'phone_mask': self._mask_phone,
            'credit_card_mask': self._mask_credit_card,
            'ssn_mask': self._mask_ssn
        }
    
    def apply_masking_to_results(self, query_results: List[Dict[str, Any]], 
                               table_name: str, masking_rules: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Apply masking rules to query results
        
        Args:
            query_results: List of dictionaries representing query results
            table_name: Name of the table being queried
            masking_rules: Column masking rules for the role
            
        Returns:
            List of dictionaries with masking applied
        """
        try:
            if not query_results or not masking_rules:
                return query_results
            
            # Get masking rules for this table
            table_masking_rules = masking_rules.get(table_name, {})
            if not table_masking_rules:
                return query_results
            
            logger.info(f"Applying masking rules to {len(query_results)} rows for table {table_name}")
            
            masked_results = []
            for row in query_results:
                masked_row = self._apply_row_masking(row, table_masking_rules)
                masked_results.append(masked_row)
            
            logger.info(f"Applied masking to {len(masked_results)} rows")
            return masked_results
            
        except Exception as e:
            logger.error(f"Error applying masking to results: {str(e)}")
            logger.error(traceback.format_exc())
            # Return original results if masking fails to avoid breaking queries
            return query_results
    
    def apply_masking_to_dataframe(self, df: pd.DataFrame, table_name: str, 
                                 masking_rules: Dict[str, Any]) -> pd.DataFrame:
        """
        Apply masking rules to a pandas DataFrame
        
        Args:
            df: DataFrame to mask
            table_name: Name of the table
            masking_rules: Column masking rules for the role
            
        Returns:
            DataFrame with masking applied
        """
        try:
            if df.empty or not masking_rules:
                return df
            
            # Get masking rules for this table
            table_masking_rules = masking_rules.get(table_name, {})
            if not table_masking_rules:
                return df
            
            logger.info(f"Applying masking rules to DataFrame with {len(df)} rows for table {table_name}")
            
            # Create a copy to avoid modifying the original
            masked_df = df.copy()
            
            for column_name, mask_type in table_masking_rules.items():
                if column_name in masked_df.columns:
                    if mask_type == 'hide':
                        # Remove the column entirely
                        masked_df = masked_df.drop(columns=[column_name])
                    else:
                        # Apply masking function
                        masking_func = self.masking_functions.get(mask_type, self._mask_value)
                        masked_df[column_name] = masked_df[column_name].apply(
                            lambda x: masking_func(x) if pd.notna(x) else x
                        )
            
            logger.info(f"Applied masking to DataFrame, resulting in {len(masked_df.columns)} columns")
            return masked_df
            
        except Exception as e:
            logger.error(f"Error applying masking to DataFrame: {str(e)}")
            logger.error(traceback.format_exc())
            # Return original DataFrame if masking fails
            return df
    
    def _apply_row_masking(self, row: Dict[str, Any], table_masking_rules: Dict[str, str]) -> Dict[str, Any]:
        """Apply masking rules to a single row"""
        try:
            masked_row = {}
            
            for column_name, value in row.items():
                if column_name in table_masking_rules:
                    mask_type = table_masking_rules[column_name]
                    
                    if mask_type == 'hide':
                        # Skip this column entirely
                        continue
                    else:
                        # Apply masking function
                        masking_func = self.masking_functions.get(mask_type, self._mask_value)
                        masked_row[column_name] = masking_func(value) if value is not None else value
                else:
                    # No masking rule, keep original value
                    masked_row[column_name] = value
            
            return masked_row
            
        except Exception as e:
            logger.error(f"Error applying row masking: {str(e)}")
            return row
    
    def _mask_value(self, value: Any) -> str:
        """Default masking function - replaces with asterisks"""
        try:
            if value is None:
                return None
            
            str_value = str(value)
            if len(str_value) <= 2:
                return '*' * len(str_value)
            else:
                return '*' * (len(str_value) - 2) + str_value[-2:]
                
        except Exception as e:
            logger.error(f"Error in default masking: {str(e)}")
            return "***"
    
    def _hash_value(self, value: Any) -> str:
        """Hash the value using SHA-256"""
        try:
            if value is None:
                return None
            
            str_value = str(value)
            hash_object = hashlib.sha256(str_value.encode())
            return hash_object.hexdigest()[:16]  # Return first 16 characters
            
        except Exception as e:
            logger.error(f"Error in hash masking: {str(e)}")
            return "hashed_value"
    
    def _hide_value(self, value: Any) -> None:
        """Hide the value completely (should not be called as column is removed)"""
        return None
    
    def _partial_mask_value(self, value: Any) -> str:
        """Partially mask the value, showing first and last characters"""
        try:
            if value is None:
                return None
            
            str_value = str(value)
            if len(str_value) <= 4:
                return '*' * len(str_value)
            else:
                return str_value[:2] + '*' * (len(str_value) - 4) + str_value[-2:]
                
        except Exception as e:
            logger.error(f"Error in partial masking: {str(e)}")
            return "***"
    
    def _mask_email(self, value: Any) -> str:
        """Mask email addresses"""
        try:
            if value is None:
                return None
            
            str_value = str(value)
            if '@' in str_value:
                local, domain = str_value.split('@', 1)
                if len(local) <= 2:
                    masked_local = '*' * len(local)
                else:
                    masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
                return f"{masked_local}@{domain}"
            else:
                return self._mask_value(value)
                
        except Exception as e:
            logger.error(f"Error in email masking: {str(e)}")
            return "***@***.com"
    
    def _mask_phone(self, value: Any) -> str:
        """Mask phone numbers"""
        try:
            if value is None:
                return None
            
            str_value = str(value)
            # Remove non-digit characters for processing
            digits_only = re.sub(r'\D', '', str_value)
            
            if len(digits_only) >= 10:
                # Mask middle digits, keep first 3 and last 4
                masked = digits_only[:3] + '*' * (len(digits_only) - 7) + digits_only[-4:]
                # Try to preserve original formatting
                if '(' in str_value and ')' in str_value:
                    return f"({masked[:3]}) {masked[3:6]}-{masked[6:]}"
                elif '-' in str_value:
                    return f"{masked[:3]}-{masked[3:6]}-{masked[6:]}"
                else:
                    return masked
            else:
                return '*' * len(str_value)
                
        except Exception as e:
            logger.error(f"Error in phone masking: {str(e)}")
            return "***-***-****"
    
    def _mask_credit_card(self, value: Any) -> str:
        """Mask credit card numbers"""
        try:
            if value is None:
                return None
            
            str_value = str(value)
            # Remove non-digit characters
            digits_only = re.sub(r'\D', '', str_value)
            
            if len(digits_only) >= 12:
                # Show first 4 and last 4 digits
                masked = digits_only[:4] + '*' * (len(digits_only) - 8) + digits_only[-4:]
                # Add formatting if original had it
                if '-' in str_value:
                    return f"{masked[:4]}-****-****-{masked[-4:]}"
                elif ' ' in str_value:
                    return f"{masked[:4]} **** **** {masked[-4:]}"
                else:
                    return masked
            else:
                return '*' * len(str_value)
                
        except Exception as e:
            logger.error(f"Error in credit card masking: {str(e)}")
            return "****-****-****-****"
    
    def _mask_ssn(self, value: Any) -> str:
        """Mask Social Security Numbers"""
        try:
            if value is None:
                return None
            
            str_value = str(value)
            # Remove non-digit characters
            digits_only = re.sub(r'\D', '', str_value)
            
            if len(digits_only) == 9:
                # Show only last 4 digits
                masked = "***-**-" + digits_only[-4:]
                return masked
            else:
                return '*' * len(str_value)
                
        except Exception as e:
            logger.error(f"Error in SSN masking: {str(e)}")
            return "***-**-****"
    
    def validate_masking_rules(self, masking_rules: Dict[str, Any], 
                             table_schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate masking rules against table schema
        
        Args:
            masking_rules: Column masking rules to validate
            table_schema: Schema information for the table
            
        Returns:
            Dictionary with validation results
        """
        try:
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'suggestions': []
            }
            
            # Get available columns from schema
            available_columns = set()
            for table_name, table_info in table_schema.get('tables', {}).items():
                for column in table_info.get('columns', []):
                    available_columns.add(f"{table_name}.{column['name']}")
            
            # Validate each masking rule
            for table_name, table_rules in masking_rules.items():
                if not isinstance(table_rules, dict):
                    validation_result['errors'].append(
                        f"Masking rules for table '{table_name}' must be a dictionary"
                    )
                    validation_result['valid'] = False
                    continue
                
                for column_name, mask_type in table_rules.items():
                    full_column_name = f"{table_name}.{column_name}"
                    
                    # Check if column exists
                    if full_column_name not in available_columns:
                        validation_result['warnings'].append(
                            f"Column '{column_name}' not found in table '{table_name}'"
                        )
                    
                    # Check if mask type is valid
                    if mask_type not in self.masking_functions:
                        validation_result['errors'].append(
                            f"Invalid mask type '{mask_type}' for column '{column_name}' in table '{table_name}'"
                        )
                        validation_result['valid'] = False
                    
                    # Provide suggestions for common data types
                    table_info = table_schema.get('tables', {}).get(table_name, {})
                    column_info = None
                    for col in table_info.get('columns', []):
                        if col['name'] == column_name:
                            column_info = col
                            break
                    
                    if column_info:
                        suggestions = self._get_masking_suggestions(column_info)
                        if suggestions and mask_type not in suggestions:
                            validation_result['suggestions'].append(
                                f"For column '{column_name}' ({column_info.get('type')}), "
                                f"consider using: {', '.join(suggestions)}"
                            )
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating masking rules: {str(e)}")
            return {
                'valid': False,
                'errors': [f'Validation error: {str(e)}'],
                'warnings': [],
                'suggestions': []
            }
    
    def _get_masking_suggestions(self, column_info: Dict[str, Any]) -> List[str]:
        """Get masking suggestions based on column type and name"""
        try:
            column_name = column_info.get('name', '').lower()
            column_type = column_info.get('type', '').lower()
            
            suggestions = []
            
            # Suggestions based on column name patterns
            if 'email' in column_name:
                suggestions.append('email_mask')
            elif 'phone' in column_name or 'mobile' in column_name:
                suggestions.append('phone_mask')
            elif 'credit' in column_name or 'card' in column_name:
                suggestions.append('credit_card_mask')
            elif 'ssn' in column_name or 'social' in column_name:
                suggestions.append('ssn_mask')
            elif 'password' in column_name or 'pwd' in column_name:
                suggestions.extend(['hash', 'hide'])
            elif 'id' in column_name and column_name != 'id':
                suggestions.append('hash')
            
            # Suggestions based on column type
            if 'varchar' in column_type or 'text' in column_type:
                if not suggestions:  # Only add generic suggestions if no specific ones
                    suggestions.extend(['mask', 'partial_mask'])
            elif 'int' in column_type or 'number' in column_type:
                suggestions.extend(['mask', 'hash'])
            
            # Always include basic options
            if not suggestions:
                suggestions.extend(['mask', 'hash', 'hide'])
            
            return list(set(suggestions))  # Remove duplicates
            
        except Exception as e:
            logger.error(f"Error getting masking suggestions: {str(e)}")
            return ['mask', 'hash', 'hide']
    
    def get_masking_statistics(self, query_results: List[Dict[str, Any]], 
                             masking_rules: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get statistics about masking applied to query results
        
        Args:
            query_results: Original query results
            masking_rules: Masking rules that were applied
            
        Returns:
            Dictionary with masking statistics
        """
        try:
            if not query_results:
                return {'total_rows': 0, 'total_columns': 0, 'masked_columns': 0}
            
            total_rows = len(query_results)
            total_columns = len(query_results[0]) if query_results else 0
            
            # Count masked columns across all tables
            masked_columns = 0
            hidden_columns = 0
            
            for table_rules in masking_rules.values():
                for mask_type in table_rules.values():
                    if mask_type == 'hide':
                        hidden_columns += 1
                    else:
                        masked_columns += 1
            
            return {
                'total_rows': total_rows,
                'total_columns': total_columns,
                'masked_columns': masked_columns,
                'hidden_columns': hidden_columns,
                'masking_applied': masked_columns > 0 or hidden_columns > 0,
                'masking_rules_count': sum(len(rules) for rules in masking_rules.values())
            }
            
        except Exception as e:
            logger.error(f"Error getting masking statistics: {str(e)}")
            return {'error': str(e)}
