"""
Database Schema Analysis Engine

This module provides comprehensive schema analysis capabilities for various database types.
It extracts detailed metadata about databases, tables, columns, indexes, and relationships.
"""

import logging
import traceback
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import sqlalchemy as sa
from sqlalchemy import create_engine, text, MetaData, inspect, Table, Column
from sqlalchemy.exc import SQLAlchemyError
from utils.database_utils import DatabaseConnectionManager

logger = logging.getLogger(__name__)

class DatabaseSchemaAnalyzer:
    """
    Analyzes database schemas and extracts comprehensive metadata
    """
    
    def __init__(self):
        self.connection_manager = DatabaseConnectionManager()
    
    def analyze_database_schema(self, db_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive schema analysis of a database
        
        Args:
            db_config: Database configuration dictionary
            
        Returns:
            Dictionary containing complete schema analysis
        """
        try:
            logger.info(f"Starting schema analysis for database: {db_config.get('name', 'Unknown')}")
            
            # Test connection first
            success, error = self.connection_manager.test_connection(db_config)
            if not success:
                return {
                    'success': False,
                    'error': f"Connection failed: {error}",
                    'analysis_timestamp': datetime.utcnow().isoformat()
                }
            
            # Get database connection
            connection = self.connection_manager.get_connection(db_config)
            
            try:
                # Create inspector for metadata extraction
                inspector = inspect(connection)
                
                # Get database-level information
                database_info = self._get_database_info(connection, db_config, inspector)
                
                # Get all tables
                tables_info = self._get_tables_info(connection, db_config, inspector)
                
                # Get relationships/foreign keys
                relationships = self._get_relationships(inspector, tables_info)
                
                # Get indexes
                indexes = self._get_indexes(inspector, tables_info)
                
                # Compile complete schema
                schema_analysis = {
                    'success': True,
                    'database_info': database_info,
                    'tables': tables_info,
                    'relationships': relationships,
                    'indexes': indexes,
                    'analysis_timestamp': datetime.utcnow().isoformat(),
                    'total_tables': len(tables_info),
                    'total_columns': sum(len(table.get('columns', [])) for table in tables_info.values())
                }
                
                logger.info(f"Schema analysis completed successfully. Found {len(tables_info)} tables")
                return schema_analysis
                
            finally:
                connection.close()
                
        except Exception as e:
            logger.error(f"Schema analysis failed: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
    
    def _get_database_info(self, connection, db_config: Dict[str, Any], inspector) -> Dict[str, Any]:
        """Get database-level information"""
        try:
            db_type = db_config['db_type'].lower()
            
            info = {
                'database_name': db_config['database_name'],
                'database_type': db_type,
                'host': db_config['host'],
                'port': db_config['port'],
                'version': None,
                'character_set': None,
                'collation': None,
                'schemas': []
            }
            
            # Get database version
            try:
                if db_type == 'postgresql':
                    result = connection.execute(text("SELECT version()"))
                    info['version'] = result.fetchone()[0]
                    
                    # Get schemas
                    result = connection.execute(text("""
                        SELECT schema_name 
                        FROM information_schema.schemata 
                        WHERE schema_name NOT IN ('information_schema', 'pg_catalog', 'pg_toast')
                        ORDER BY schema_name
                    """))
                    info['schemas'] = [row[0] for row in result.fetchall()]
                    
                elif db_type == 'mysql':
                    result = connection.execute(text("SELECT VERSION()"))
                    info['version'] = result.fetchone()[0]
                    
                    # Get character set and collation
                    result = connection.execute(text("""
                        SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
                        FROM information_schema.SCHEMATA 
                        WHERE SCHEMA_NAME = %s
                    """), (db_config['database_name'],))
                    row = result.fetchone()
                    if row:
                        info['character_set'] = row[0]
                        info['collation'] = row[1]
                        
                elif db_type == 'sqlserver':
                    result = connection.execute(text("SELECT @@VERSION"))
                    info['version'] = result.fetchone()[0]
                    
                    # Get schemas
                    result = connection.execute(text("""
                        SELECT name FROM sys.schemas 
                        WHERE name NOT IN ('sys', 'INFORMATION_SCHEMA', 'guest', 'db_owner', 'db_accessadmin', 'db_securityadmin', 'db_ddladmin', 'db_backupoperator', 'db_datareader', 'db_datawriter', 'db_denydatareader', 'db_denydatawriter')
                        ORDER BY name
                    """))
                    info['schemas'] = [row[0] for row in result.fetchall()]
                    
            except Exception as e:
                logger.warning(f"Could not get database version/info: {str(e)}")
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting database info: {str(e)}")
            return {'error': str(e)}
    
    def _get_tables_info(self, connection, db_config: Dict[str, Any], inspector) -> Dict[str, Dict[str, Any]]:
        """Get detailed information about all tables"""
        try:
            tables_info = {}
            table_names = inspector.get_table_names()
            
            # Also get views if supported
            try:
                view_names = inspector.get_view_names()
                logger.info(f"Found {len(table_names)} tables and {len(view_names)} views")
            except:
                view_names = []
                logger.info(f"Found {len(table_names)} tables")
            
            # Process tables
            for table_name in table_names:
                table_info = self._analyze_table(connection, db_config, inspector, table_name, 'table')
                if table_info:
                    tables_info[table_name] = table_info
            
            # Process views
            for view_name in view_names:
                view_info = self._analyze_table(connection, db_config, inspector, view_name, 'view')
                if view_info:
                    tables_info[view_name] = view_info
            
            return tables_info
            
        except Exception as e:
            logger.error(f"Error getting tables info: {str(e)}")
            return {}
    
    def _analyze_table(self, connection, db_config: Dict[str, Any], inspector, table_name: str, table_type: str = 'table') -> Optional[Dict[str, Any]]:
        """Analyze a single table or view"""
        try:
            # Get columns information
            columns = inspector.get_columns(table_name)
            
            # Get primary key
            try:
                pk_constraint = inspector.get_pk_constraint(table_name)
                primary_keys = pk_constraint.get('constrained_columns', []) if pk_constraint else []
            except:
                primary_keys = []
            
            # Get foreign keys
            try:
                foreign_keys = inspector.get_foreign_keys(table_name)
            except:
                foreign_keys = []
            
            # Get table statistics (row count, size, etc.)
            table_stats = self._get_table_statistics(connection, db_config, table_name)
            
            # Process columns
            columns_info = []
            for col in columns:
                col_info = {
                    'name': col['name'],
                    'type': str(col['type']),
                    'nullable': col.get('nullable', True),
                    'default': str(col.get('default')) if col.get('default') is not None else None,
                    'autoincrement': col.get('autoincrement', False),
                    'primary_key': col['name'] in primary_keys,
                    'comment': col.get('comment', ''),
                }
                
                # Add type-specific information
                if hasattr(col['type'], 'length') and col['type'].length:
                    col_info['length'] = col['type'].length
                if hasattr(col['type'], 'precision') and col['type'].precision:
                    col_info['precision'] = col['type'].precision
                if hasattr(col['type'], 'scale') and col['type'].scale:
                    col_info['scale'] = col['type'].scale
                
                columns_info.append(col_info)
            
            table_info = {
                'name': table_name,
                'type': table_type,
                'columns': columns_info,
                'primary_keys': primary_keys,
                'foreign_keys': foreign_keys,
                'column_count': len(columns_info),
                'statistics': table_stats
            }
            
            return table_info
            
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {str(e)}")
            return None
    
    def _get_table_statistics(self, connection, db_config: Dict[str, Any], table_name: str) -> Dict[str, Any]:
        """Get table statistics like row count, size, etc."""
        try:
            db_type = db_config['db_type'].lower()
            stats = {}
            
            # Get row count
            try:
                result = connection.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                stats['row_count'] = result.fetchone()[0]
            except Exception as e:
                logger.warning(f"Could not get row count for {table_name}: {str(e)}")
                stats['row_count'] = None
            
            # Get table size (database-specific)
            try:
                if db_type == 'postgresql':
                    result = connection.execute(text("""
                        SELECT pg_total_relation_size(%s) as total_size,
                               pg_relation_size(%s) as table_size
                    """), (table_name, table_name))
                    row = result.fetchone()
                    if row:
                        stats['total_size_bytes'] = row[0]
                        stats['table_size_bytes'] = row[1]
                        
                elif db_type == 'mysql':
                    result = connection.execute(text("""
                        SELECT data_length + index_length as total_size,
                               data_length as table_size
                        FROM information_schema.tables 
                        WHERE table_schema = %s AND table_name = %s
                    """), (db_config['database_name'], table_name))
                    row = result.fetchone()
                    if row:
                        stats['total_size_bytes'] = row[0]
                        stats['table_size_bytes'] = row[1]
                        
            except Exception as e:
                logger.warning(f"Could not get size info for {table_name}: {str(e)}")
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting table statistics for {table_name}: {str(e)}")
            return {}
    
    def _get_relationships(self, inspector, tables_info: Dict[str, Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract relationships between tables"""
        relationships = []
        
        try:
            for table_name, table_info in tables_info.items():
                for fk in table_info.get('foreign_keys', []):
                    relationship = {
                        'from_table': table_name,
                        'from_columns': fk.get('constrained_columns', []),
                        'to_table': fk.get('referred_table'),
                        'to_columns': fk.get('referred_columns', []),
                        'constraint_name': fk.get('name'),
                        'on_delete': fk.get('options', {}).get('ondelete'),
                        'on_update': fk.get('options', {}).get('onupdate')
                    }
                    relationships.append(relationship)
            
            logger.info(f"Found {len(relationships)} relationships")
            return relationships
            
        except Exception as e:
            logger.error(f"Error extracting relationships: {str(e)}")
            return []
    
    def _get_indexes(self, inspector, tables_info: Dict[str, Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Get index information for all tables"""
        indexes = {}
        
        try:
            for table_name in tables_info.keys():
                try:
                    table_indexes = inspector.get_indexes(table_name)
                    indexes[table_name] = table_indexes
                except Exception as e:
                    logger.warning(f"Could not get indexes for table {table_name}: {str(e)}")
                    indexes[table_name] = []
            
            total_indexes = sum(len(idx_list) for idx_list in indexes.values())
            logger.info(f"Found {total_indexes} indexes across all tables")
            return indexes
            
        except Exception as e:
            logger.error(f"Error getting indexes: {str(e)}")
            return {}
