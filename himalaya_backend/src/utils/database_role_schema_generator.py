"""
Database Role Schema Generator

This module generates filtered database schemas for roles based on their access restrictions.
It creates role-specific schemas that only include tables and columns the role can access.
"""

import logging
import traceback
from typing import Dict, List, Any, Optional, Set
from datetime import datetime
import copy

from models.models import DatabaseRole, DatabaseSchema, DatabaseSemanticSummary

logger = logging.getLogger(__name__)

class DatabaseRoleSchemaGenerator:
    """
    Generates role-specific database schemas based on access restrictions
    """
    
    def __init__(self):
        pass
    
    def generate_role_schema(self, role: DatabaseRole) -> Dict[str, Any]:
        """
        Generate a filtered schema for a specific role
        
        Args:
            role: DatabaseRole object with access restrictions
            
        Returns:
            Dictionary containing the filtered schema for the role
        """
        try:
            logger.info(f"Generating role schema for role: {role.name}")
            
            # Get the full database schema
            full_schema = DatabaseSchema.query.filter_by(
                external_database_id=role.external_database_id
            ).first()
            
            if not full_schema:
                raise ValueError(f"No schema found for database ID {role.external_database_id}")
            
            # Start with the full schema data
            full_schema_data = full_schema.schema_data
            
            # Apply role restrictions
            filtered_schema = self._apply_role_restrictions(full_schema_data, role)
            
            # Add role-specific metadata
            filtered_schema['role_metadata'] = {
                'role_id': role.id,
                'role_name': role.name,
                'database_id': role.external_database_id,
                'where_conditions': role.where_conditions,
                'column_masking_applied': bool(role.column_masking_rules),
                'generated_at': datetime.utcnow().isoformat(),
                'total_accessible_tables': len(filtered_schema.get('tables', {})),
                'restrictions_applied': {
                    'table_filtering': bool(role.allowed_tables or role.restricted_tables),
                    'column_masking': bool(role.column_masking_rules),
                    'where_conditions': bool(role.where_conditions)
                }
            }
            
            # Store the role schema
            role.role_schema = filtered_schema
            
            logger.info(f"Generated role schema with {len(filtered_schema.get('tables', {}))} accessible tables")
            return filtered_schema
            
        except Exception as e:
            logger.error(f"Error generating role schema: {str(e)}")
            logger.error(traceback.format_exc())
            raise
    
    def _apply_role_restrictions(self, full_schema: Dict[str, Any], role: DatabaseRole) -> Dict[str, Any]:
        """Apply role restrictions to the full schema"""
        try:
            # Deep copy to avoid modifying the original
            filtered_schema = copy.deepcopy(full_schema)
            
            # Get all tables from the schema
            all_tables = filtered_schema.get('tables', {})
            
            # Apply table-level restrictions
            accessible_tables = self._filter_accessible_tables(all_tables, role)
            
            # Apply column-level restrictions and masking
            for table_name in list(accessible_tables.keys()):
                accessible_tables[table_name] = self._apply_column_restrictions(
                    accessible_tables[table_name], table_name, role
                )
            
            # Update the filtered schema
            filtered_schema['tables'] = accessible_tables
            
            # Update counts
            filtered_schema['total_tables'] = len(accessible_tables)
            filtered_schema['total_columns'] = sum(
                len(table.get('columns', [])) for table in accessible_tables.values()
            )
            
            # Update relationships to only include accessible tables
            filtered_schema['relationships'] = self._filter_relationships(
                filtered_schema.get('relationships', []), accessible_tables
            )
            
            # Update indexes to only include accessible tables
            filtered_schema['indexes'] = self._filter_indexes(
                filtered_schema.get('indexes', {}), accessible_tables
            )
            
            return filtered_schema
            
        except Exception as e:
            logger.error(f"Error applying role restrictions: {str(e)}")
            raise
    
    def _filter_accessible_tables(self, all_tables: Dict[str, Any], role: DatabaseRole) -> Dict[str, Any]:
        """Filter tables based on role's allowed/restricted tables"""
        try:
            accessible_tables = {}
            
            for table_name, table_info in all_tables.items():
                # Check if table is accessible based on role restrictions
                if self._is_table_accessible(table_name, role):
                    accessible_tables[table_name] = table_info
            
            logger.info(f"Filtered {len(all_tables)} tables to {len(accessible_tables)} accessible tables")
            return accessible_tables
            
        except Exception as e:
            logger.error(f"Error filtering accessible tables: {str(e)}")
            raise
    
    def _is_table_accessible(self, table_name: str, role: DatabaseRole) -> bool:
        """Check if a table is accessible for the given role"""
        try:
            # If allowed_tables is specified, table must be in the list
            if role.allowed_tables:
                if table_name not in role.allowed_tables:
                    return False
            
            # If restricted_tables is specified, table must not be in the list
            if role.restricted_tables:
                if table_name in role.restricted_tables:
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking table accessibility: {str(e)}")
            return False
    
    def _apply_column_restrictions(self, table_info: Dict[str, Any], table_name: str, 
                                 role: DatabaseRole) -> Dict[str, Any]:
        """Apply column-level restrictions and masking to a table"""
        try:
            # Deep copy to avoid modifying original
            filtered_table = copy.deepcopy(table_info)
            
            # Get column masking rules for this table
            masking_rules = {}
            if role.column_masking_rules and table_name in role.column_masking_rules:
                masking_rules = role.column_masking_rules[table_name]
            
            # Process columns
            filtered_columns = []
            for column in filtered_table.get('columns', []):
                column_name = column['name']
                
                # Check if column should be masked or hidden
                if column_name in masking_rules:
                    mask_type = masking_rules[column_name]
                    
                    if mask_type == 'hide':
                        # Skip this column entirely
                        continue
                    elif mask_type == 'mask':
                        # Mark column as masked but keep it in schema
                        column = copy.deepcopy(column)
                        column['masked'] = True
                        column['mask_type'] = 'mask'
                        column['comment'] = f"{column.get('comment', '')} [MASKED]".strip()
                    elif mask_type == 'hash':
                        # Mark column as hashed
                        column = copy.deepcopy(column)
                        column['masked'] = True
                        column['mask_type'] = 'hash'
                        column['comment'] = f"{column.get('comment', '')} [HASHED]".strip()
                
                filtered_columns.append(column)
            
            # Update table info
            filtered_table['columns'] = filtered_columns
            filtered_table['column_count'] = len(filtered_columns)
            
            # Update primary keys to exclude hidden columns
            if 'primary_keys' in filtered_table:
                visible_columns = {col['name'] for col in filtered_columns}
                filtered_table['primary_keys'] = [
                    pk for pk in filtered_table['primary_keys'] 
                    if pk in visible_columns
                ]
            
            # Update foreign keys to exclude hidden columns
            if 'foreign_keys' in filtered_table:
                filtered_fks = []
                for fk in filtered_table['foreign_keys']:
                    # Check if all constrained columns are visible
                    constrained_cols = fk.get('constrained_columns', [])
                    visible_constrained = [
                        col for col in constrained_cols 
                        if col in {c['name'] for c in filtered_columns}
                    ]
                    
                    if visible_constrained:
                        fk_copy = copy.deepcopy(fk)
                        fk_copy['constrained_columns'] = visible_constrained
                        filtered_fks.append(fk_copy)
                
                filtered_table['foreign_keys'] = filtered_fks
            
            # Add role restriction metadata
            filtered_table['role_restrictions'] = {
                'masked_columns': [
                    col['name'] for col in filtered_columns 
                    if col.get('masked', False)
                ],
                'hidden_columns': [
                    col_name for col_name in masking_rules.keys()
                    if masking_rules[col_name] == 'hide'
                ],
                'original_column_count': len(table_info.get('columns', [])),
                'filtered_column_count': len(filtered_columns)
            }
            
            return filtered_table
            
        except Exception as e:
            logger.error(f"Error applying column restrictions to table {table_name}: {str(e)}")
            raise
    
    def _filter_relationships(self, relationships: List[Dict[str, Any]], 
                            accessible_tables: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter relationships to only include accessible tables"""
        try:
            filtered_relationships = []
            accessible_table_names = set(accessible_tables.keys())
            
            for relationship in relationships:
                from_table = relationship.get('from_table')
                to_table = relationship.get('to_table')
                
                # Only include relationships where both tables are accessible
                if from_table in accessible_table_names and to_table in accessible_table_names:
                    # Check if the columns involved are still visible
                    from_table_info = accessible_tables[from_table]
                    to_table_info = accessible_tables[to_table]
                    
                    from_visible_cols = {col['name'] for col in from_table_info.get('columns', [])}
                    to_visible_cols = {col['name'] for col in to_table_info.get('columns', [])}
                    
                    from_cols = relationship.get('from_columns', [])
                    to_cols = relationship.get('to_columns', [])
                    
                    # Check if all relationship columns are visible
                    if (all(col in from_visible_cols for col in from_cols) and
                        all(col in to_visible_cols for col in to_cols)):
                        filtered_relationships.append(relationship)
            
            logger.info(f"Filtered {len(relationships)} relationships to {len(filtered_relationships)}")
            return filtered_relationships
            
        except Exception as e:
            logger.error(f"Error filtering relationships: {str(e)}")
            return []
    
    def _filter_indexes(self, indexes: Dict[str, List[Dict[str, Any]]], 
                       accessible_tables: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Filter indexes to only include accessible tables and columns"""
        try:
            filtered_indexes = {}
            
            for table_name, table_indexes in indexes.items():
                if table_name in accessible_tables:
                    table_info = accessible_tables[table_name]
                    visible_columns = {col['name'] for col in table_info.get('columns', [])}
                    
                    filtered_table_indexes = []
                    for index in table_indexes:
                        index_columns = index.get('column_names', [])
                        
                        # Only include indexes where all columns are visible
                        if all(col in visible_columns for col in index_columns):
                            filtered_table_indexes.append(index)
                    
                    if filtered_table_indexes:
                        filtered_indexes[table_name] = filtered_table_indexes
            
            return filtered_indexes
            
        except Exception as e:
            logger.error(f"Error filtering indexes: {str(e)}")
            return {}
    
    def get_role_semantic_context(self, role: DatabaseRole) -> Dict[str, Any]:
        """
        Get semantic context for a role including summaries and business descriptions
        """
        try:
            # Get role-specific semantic summaries
            summaries = DatabaseSemanticSummary.query.filter_by(
                database_role_id=role.id,
                is_active=True
            ).all()
            
            # Get database-level summaries
            db_summaries = DatabaseSemanticSummary.query.filter_by(
                external_database_id=role.external_database_id,
                database_role_id=None,  # Database-level summaries
                is_active=True
            ).all()
            
            context = {
                'role_id': role.id,
                'role_name': role.name,
                'database_id': role.external_database_id,
                'role_summaries': {},
                'database_summaries': {},
                'business_context': {
                    'role_description': role.description,
                    'access_restrictions': {
                        'where_conditions': role.where_conditions,
                        'allowed_tables': role.allowed_tables,
                        'restricted_tables': role.restricted_tables,
                        'column_masking': bool(role.column_masking_rules)
                    }
                }
            }
            
            # Process role-specific summaries
            for summary in summaries:
                key = f"{summary.summary_type}_{summary.target_name}"
                context['role_summaries'][key] = {
                    'type': summary.summary_type,
                    'target': summary.target_name,
                    'summary': summary.current_summary,
                    'business_description': summary.business_description,
                    'usage_patterns': summary.usage_patterns,
                    'version': summary.version
                }
            
            # Process database-level summaries
            for summary in db_summaries:
                key = f"{summary.summary_type}_{summary.target_name}"
                context['database_summaries'][key] = {
                    'type': summary.summary_type,
                    'target': summary.target_name,
                    'summary': summary.current_summary,
                    'business_description': summary.business_description,
                    'usage_patterns': summary.usage_patterns,
                    'version': summary.version
                }
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting role semantic context: {str(e)}")
            return {}
    
    def validate_role_schema(self, role: DatabaseRole) -> Dict[str, Any]:
        """
        Validate that a role's schema is properly configured
        """
        try:
            validation_result = {
                'valid': True,
                'errors': [],
                'warnings': [],
                'statistics': {}
            }
            
            # Check if role has a generated schema
            if not role.role_schema:
                validation_result['valid'] = False
                validation_result['errors'].append('Role schema not generated')
                return validation_result
            
            # Check accessible tables
            accessible_tables = role.role_schema.get('tables', {})
            if not accessible_tables:
                validation_result['valid'] = False
                validation_result['errors'].append('No accessible tables found for role')
            
            # Check for tables with no accessible columns
            empty_tables = []
            for table_name, table_info in accessible_tables.items():
                if not table_info.get('columns'):
                    empty_tables.append(table_name)
            
            if empty_tables:
                validation_result['warnings'].append(
                    f"Tables with no accessible columns: {', '.join(empty_tables)}"
                )
            
            # Collect statistics
            validation_result['statistics'] = {
                'accessible_tables': len(accessible_tables),
                'total_accessible_columns': sum(
                    len(table.get('columns', [])) for table in accessible_tables.values()
                ),
                'masked_columns': sum(
                    len(table.get('role_restrictions', {}).get('masked_columns', []))
                    for table in accessible_tables.values()
                ),
                'hidden_columns': sum(
                    len(table.get('role_restrictions', {}).get('hidden_columns', []))
                    for table in accessible_tables.values()
                )
            }
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating role schema: {str(e)}")
            return {
                'valid': False,
                'errors': [f'Validation error: {str(e)}'],
                'warnings': [],
                'statistics': {}
            }
