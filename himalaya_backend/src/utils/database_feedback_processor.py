"""
Database Feedback Processing Engine

This module processes trainer feedback and automatically updates semantic summaries
based on Q&A interactions and feedback patterns.
"""

import logging
import traceback
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from collections import defaultdict

from models.models import (
    db, TrainingInteraction, DatabaseSemanticSummary, DatabaseRole,
    DatabaseTable, DatabaseSchema
)
from utils.openai_utils import openai_client
from config.settings import AZURE_OPENAI_DEPLOYMENT_NAME

logger = logging.getLogger(__name__)

class DatabaseFeedbackProcessor:
    """
    Processes trainer feedback and generates summary improvements
    """
    
    def __init__(self):
        self.client = openai_client
        self.model = AZURE_OPENAI_DEPLOYMENT_NAME
        self.max_tokens = 1500
    
    def process_training_session_feedback(self, session_id: int) -> Dict[str, Any]:
        """
        Process all feedback from a training session and generate summary corrections
        
        Args:
            session_id: ID of the training session to process
            
        Returns:
            Dictionary containing processing results and suggested corrections
        """
        try:
            logger.info(f"Processing feedback for training session {session_id}")
            
            # Get all interactions with feedback from the session
            interactions = TrainingInteraction.query.filter_by(
                training_session_id=session_id
            ).filter(
                TrainingInteraction.feedback_rating.isnot(None)
            ).all()
            
            if not interactions:
                return {
                    'success': True,
                    'message': 'No feedback found to process',
                    'corrections': []
                }
            
            # Analyze feedback patterns
            feedback_analysis = self._analyze_feedback_patterns(interactions)
            
            # Generate summary corrections
            corrections = self._generate_summary_corrections(interactions, feedback_analysis)
            
            # Apply corrections if they meet quality thresholds
            applied_corrections = self._apply_corrections(corrections)
            
            # Update interaction records
            for interaction in interactions:
                if not interaction.applied_corrections:
                    interaction.summary_corrections = corrections
                    interaction.applied_corrections = True
            
            db.session.commit()
            
            logger.info(f"Processed {len(interactions)} interactions, generated {len(corrections)} corrections")
            
            return {
                'success': True,
                'interactions_processed': len(interactions),
                'corrections_generated': len(corrections),
                'corrections_applied': len(applied_corrections),
                'feedback_analysis': feedback_analysis,
                'corrections': corrections,
                'applied_corrections': applied_corrections
            }
            
        except Exception as e:
            logger.error(f"Error processing training session feedback: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e)
            }
    
    def _analyze_feedback_patterns(self, interactions: List[TrainingInteraction]) -> Dict[str, Any]:
        """Analyze patterns in trainer feedback"""
        try:
            analysis = {
                'total_interactions': len(interactions),
                'average_rating': 0,
                'rating_distribution': defaultdict(int),
                'common_issues': [],
                'improvement_themes': [],
                'table_feedback': defaultdict(list),
                'query_patterns': defaultdict(list)
            }
            
            total_rating = 0
            feedback_texts = []
            improvement_suggestions = []
            
            for interaction in interactions:
                # Rating analysis
                if interaction.feedback_rating:
                    total_rating += interaction.feedback_rating
                    analysis['rating_distribution'][interaction.feedback_rating] += 1
                
                # Collect feedback text
                if interaction.feedback_text:
                    feedback_texts.append(interaction.feedback_text)
                
                # Collect improvement suggestions
                if interaction.suggested_improvements:
                    improvement_suggestions.append(interaction.suggested_improvements)
                
                # Analyze SQL queries for table patterns
                if interaction.sql_query:
                    tables_mentioned = self._extract_tables_from_sql(interaction.sql_query)
                    for table in tables_mentioned:
                        analysis['table_feedback'][table].append({
                            'rating': interaction.feedback_rating,
                            'feedback': interaction.feedback_text,
                            'query': interaction.sql_query
                        })
            
            # Calculate average rating
            if total_rating > 0:
                analysis['average_rating'] = total_rating / len(interactions)
            
            # Extract common themes using LLM
            if feedback_texts:
                analysis['common_issues'] = self._extract_feedback_themes(
                    feedback_texts, 'issues'
                )
            
            if improvement_suggestions:
                analysis['improvement_themes'] = self._extract_feedback_themes(
                    improvement_suggestions, 'improvements'
                )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing feedback patterns: {str(e)}")
            return {'error': str(e)}
    
    def _extract_tables_from_sql(self, sql_query: str) -> List[str]:
        """Extract table names from SQL query"""
        try:
            # Simple regex-based extraction (could be improved with SQL parser)
            import re
            
            # Look for FROM and JOIN clauses
            from_pattern = r'FROM\s+([a-zA-Z_][a-zA-Z0-9_]*)'
            join_pattern = r'JOIN\s+([a-zA-Z_][a-zA-Z0-9_]*)'
            
            tables = set()
            
            for match in re.finditer(from_pattern, sql_query, re.IGNORECASE):
                tables.add(match.group(1).lower())
            
            for match in re.finditer(join_pattern, sql_query, re.IGNORECASE):
                tables.add(match.group(1).lower())
            
            return list(tables)
            
        except Exception as e:
            logger.error(f"Error extracting tables from SQL: {str(e)}")
            return []
    
    def _extract_feedback_themes(self, feedback_list: List[str], theme_type: str) -> List[str]:
        """Use LLM to extract common themes from feedback"""
        try:
            feedback_text = "\n".join(feedback_list)
            
            prompt = f"""
Analyze the following trainer feedback and extract the main {theme_type} themes.
Return a JSON list of the top 5 most common themes, each as a short phrase.

Feedback:
{feedback_text}

Return only a JSON array of strings, no other text.
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at analyzing feedback and extracting themes. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )
            
            themes_text = response.choices[0].message.content.strip()
            themes = json.loads(themes_text)
            
            return themes if isinstance(themes, list) else []
            
        except Exception as e:
            logger.error(f"Error extracting feedback themes: {str(e)}")
            return []
    
    def _generate_summary_corrections(self, interactions: List[TrainingInteraction], 
                                    feedback_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate specific corrections for semantic summaries based on feedback"""
        try:
            corrections = []
            
            # Get the role being trained
            if not interactions:
                return corrections
            
            session = interactions[0].session
            role = session.role
            
            # Get current summaries for this role
            summaries = DatabaseSemanticSummary.query.filter_by(
                database_role_id=role.id,
                is_active=True
            ).all()
            
            # Also get database-level summaries
            db_summaries = DatabaseSemanticSummary.query.filter_by(
                external_database_id=role.external_database_id,
                database_role_id=None,
                is_active=True
            ).all()
            
            all_summaries = summaries + db_summaries
            
            # Generate corrections for each summary based on feedback
            for summary in all_summaries:
                correction = self._generate_summary_correction(
                    summary, interactions, feedback_analysis
                )
                if correction:
                    corrections.append(correction)
            
            return corrections
            
        except Exception as e:
            logger.error(f"Error generating summary corrections: {str(e)}")
            return []
    
    def _generate_summary_correction(self, summary: DatabaseSemanticSummary,
                                   interactions: List[TrainingInteraction],
                                   feedback_analysis: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Generate a specific correction for one summary"""
        try:
            # Filter interactions relevant to this summary
            relevant_interactions = []
            
            if summary.summary_type == 'table' and summary.table:
                table_name = summary.table.table_name
                for interaction in interactions:
                    if interaction.sql_query and table_name.lower() in interaction.sql_query.lower():
                        relevant_interactions.append(interaction)
            elif summary.summary_type == 'database':
                # All interactions are relevant for database-level summaries
                relevant_interactions = interactions
            
            if not relevant_interactions:
                return None
            
            # Collect feedback for this summary
            feedback_items = []
            for interaction in relevant_interactions:
                if interaction.feedback_rating and interaction.feedback_rating < 4:  # Poor ratings
                    feedback_items.append({
                        'question': interaction.question,
                        'answer': interaction.system_answer,
                        'rating': interaction.feedback_rating,
                        'feedback': interaction.feedback_text,
                        'suggestions': interaction.suggested_improvements
                    })
            
            if not feedback_items:
                return None
            
            # Generate correction using LLM
            correction_prompt = self._build_correction_prompt(summary, feedback_items, feedback_analysis)
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at improving database documentation based on user feedback."},
                    {"role": "user", "content": correction_prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.3
            )
            
            correction_text = response.choices[0].message.content.strip()
            
            # Parse the correction
            try:
                correction_data = json.loads(correction_text)
            except json.JSONDecodeError:
                # If not valid JSON, treat as plain text correction
                correction_data = {
                    'suggested_summary': correction_text,
                    'changes_made': ['General improvements based on feedback'],
                    'confidence': 0.7
                }
            
            return {
                'summary_id': summary.id,
                'summary_type': summary.summary_type,
                'target_name': summary.target_name,
                'current_summary': summary.current_summary,
                'suggested_summary': correction_data.get('suggested_summary', ''),
                'changes_made': correction_data.get('changes_made', []),
                'confidence': correction_data.get('confidence', 0.5),
                'relevant_interactions': len(relevant_interactions),
                'feedback_items': len(feedback_items)
            }
            
        except Exception as e:
            logger.error(f"Error generating summary correction: {str(e)}")
            return None
    
    def _build_correction_prompt(self, summary: DatabaseSemanticSummary,
                               feedback_items: List[Dict[str, Any]],
                               feedback_analysis: Dict[str, Any]) -> str:
        """Build prompt for LLM to generate summary corrections"""
        
        prompt = f"""
Based on trainer feedback, improve the following database summary.

Current Summary:
Type: {summary.summary_type}
Target: {summary.target_name}
Summary: {summary.current_summary}

Trainer Feedback:
"""
        
        for i, item in enumerate(feedback_items, 1):
            prompt += f"""
Feedback {i}:
- Question: {item['question']}
- System Answer: {item['answer'][:200]}...
- Rating: {item['rating']}/5
- Feedback: {item['feedback']}
- Suggestions: {item['suggestions']}
"""
        
        prompt += f"""

Common Issues Identified: {', '.join(feedback_analysis.get('common_issues', []))}
Improvement Themes: {', '.join(feedback_analysis.get('improvement_themes', []))}

Please provide an improved summary that addresses the feedback. Return your response as JSON with:
{{
    "suggested_summary": "improved summary text",
    "changes_made": ["list of specific changes made"],
    "confidence": 0.8
}}

Focus on:
1. Addressing specific feedback points
2. Improving clarity and accuracy
3. Adding missing business context
4. Correcting any misconceptions
"""
        
        return prompt
    
    def _apply_corrections(self, corrections: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply corrections that meet quality thresholds"""
        try:
            applied_corrections = []
            
            for correction in corrections:
                # Only apply corrections with high confidence
                if correction.get('confidence', 0) >= 0.7:
                    summary = DatabaseSemanticSummary.query.get(correction['summary_id'])
                    if summary:
                        # Update the summary
                        summary.human_edited_summary = correction['suggested_summary']
                        summary.current_summary = correction['suggested_summary']
                        summary.updated_at = datetime.utcnow()
                        summary.version += 1
                        summary.token_count = len(correction['suggested_summary'].split())
                        summary.last_edited_by = 1  # System user
                        
                        applied_corrections.append(correction)
                        
                        logger.info(f"Auto-applied correction to summary {summary.id}")
            
            return applied_corrections
            
        except Exception as e:
            logger.error(f"Error applying corrections: {str(e)}")
            return []
    
    def generate_training_report(self, session_id: int) -> Dict[str, Any]:
        """Generate a comprehensive training report for a session"""
        try:
            session = db.session.query(TrainingSession).get(session_id)
            if not session:
                return {'error': 'Training session not found'}
            
            # Get all interactions
            interactions = TrainingInteraction.query.filter_by(
                training_session_id=session_id
            ).all()
            
            # Analyze feedback
            feedback_analysis = self._analyze_feedback_patterns(interactions)
            
            # Get role information
            role = session.role
            
            # Generate report
            report = {
                'session_info': {
                    'id': session.id,
                    'role_name': role.name,
                    'trainer': session.trainer.user_name,
                    'started_at': session.started_at.isoformat() if session.started_at else None,
                    'completed_at': session.completed_at.isoformat() if session.completed_at else None,
                    'status': session.status
                },
                'interaction_summary': {
                    'total_interactions': len(interactions),
                    'interactions_with_feedback': len([i for i in interactions if i.feedback_rating]),
                    'average_rating': feedback_analysis.get('average_rating', 0),
                    'rating_distribution': dict(feedback_analysis.get('rating_distribution', {}))
                },
                'feedback_analysis': feedback_analysis,
                'recommendations': self._generate_training_recommendations(feedback_analysis),
                'next_steps': self._generate_next_steps(role, feedback_analysis)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating training report: {str(e)}")
            return {'error': str(e)}
    
    def _generate_training_recommendations(self, feedback_analysis: Dict[str, Any]) -> List[str]:
        """Generate training recommendations based on feedback analysis"""
        recommendations = []
        
        avg_rating = feedback_analysis.get('average_rating', 0)
        
        if avg_rating < 3.0:
            recommendations.append("Focus on improving basic query accuracy and response quality")
        elif avg_rating < 4.0:
            recommendations.append("Work on adding more business context and detailed explanations")
        else:
            recommendations.append("Continue with advanced training scenarios and edge cases")
        
        common_issues = feedback_analysis.get('common_issues', [])
        if 'accuracy' in str(common_issues).lower():
            recommendations.append("Review and update semantic summaries for better accuracy")
        
        if 'context' in str(common_issues).lower():
            recommendations.append("Add more business context to database and table summaries")
        
        return recommendations
    
    def _generate_next_steps(self, role: DatabaseRole, feedback_analysis: Dict[str, Any]) -> List[str]:
        """Generate next steps for role training"""
        next_steps = []
        
        avg_rating = feedback_analysis.get('average_rating', 0)
        
        if avg_rating >= 4.0 and role.training_status != 'completed':
            next_steps.append("Role is ready for production - consider publishing")
        elif avg_rating >= 3.5:
            next_steps.append("Conduct one more training session to reach production readiness")
        else:
            next_steps.append("Continue training with focus on identified improvement areas")
        
        if not role.is_published:
            next_steps.append("Review and update role permissions and access restrictions")
        
        return next_steps
