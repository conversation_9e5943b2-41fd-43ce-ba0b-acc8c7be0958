"""
Database Memory Management Engine

This module implements MemGPT-style memory management for database chat sessions.
It manages core memory, archival memory, and recall memory for both database and user contexts.
"""

import logging
import traceback
import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta

from models.models import (
    db, DatabaseMemoryContext, UserMemoryContext, MemoryUpdate,
    DatabaseRole, User, EnhancedDatabaseChatMessage
)
from utils.openai_utils import openai_client
from config.settings import AZURE_OPENAI_DEPLOYMENT_NAME

logger = logging.getLogger(__name__)

class DatabaseMemoryManager:
    """
    MemGPT-style memory management for database chat sessions
    """
    
    def __init__(self):
        self.client = openai_client
        self.model = AZURE_OPENAI_DEPLOYMENT_NAME
        self.max_tokens = 1500
        
        # Memory size limits (in tokens)
        self.core_memory_limit = 2000
        self.recall_memory_limit = 6000
        self.archival_memory_limit = 50000
    
    def get_or_create_database_memory(self, database_role_id: int) -> DatabaseMemoryContext:
        """Get or create database memory context for a role"""
        try:
            memory = DatabaseMemoryContext.query.filter_by(
                database_role_id=database_role_id
            ).first()
            
            if not memory:
                # Create new database memory context
                role = DatabaseRole.query.get(database_role_id)
                if not role:
                    raise ValueError(f"Database role {database_role_id} not found")
                
                initial_core_memory = self._create_initial_database_core_memory(role)
                
                memory = DatabaseMemoryContext(
                    external_database_id=role.external_database_id,
                    database_role_id=database_role_id,
                    core_memory=initial_core_memory,
                    archival_memory={'entries': []},
                    recall_memory={'recent_interactions': []}
                )
                
                db.session.add(memory)
                db.session.commit()
                
                logger.info(f"Created new database memory context for role {database_role_id}")
            
            return memory
            
        except Exception as e:
            logger.error(f"Error getting/creating database memory: {str(e)}")
            raise
    
    def get_or_create_user_memory(self, user_id: int, database_role_id: int) -> UserMemoryContext:
        """Get or create user memory context for a specific role"""
        try:
            memory = UserMemoryContext.query.filter_by(
                user_id=user_id,
                database_role_id=database_role_id
            ).first()
            
            if not memory:
                # Create new user memory context
                user = User.query.get(user_id)
                if not user:
                    raise ValueError(f"User {user_id} not found")
                
                initial_core_memory = self._create_initial_user_core_memory(user, database_role_id)
                
                memory = UserMemoryContext(
                    user_id=user_id,
                    database_role_id=database_role_id,
                    core_memory=initial_core_memory,
                    archival_memory={'entries': []},
                    recall_memory={'recent_queries': [], 'preferences': {}}
                )
                
                db.session.add(memory)
                db.session.commit()
                
                logger.info(f"Created new user memory context for user {user_id}, role {database_role_id}")
            
            return memory
            
        except Exception as e:
            logger.error(f"Error getting/creating user memory: {str(e)}")
            raise
    
    def update_memory_from_interaction(self, message: EnhancedDatabaseChatMessage) -> Dict[str, Any]:
        """Update memory contexts based on a chat interaction"""
        try:
            logger.info(f"Updating memory from interaction {message.id}")
            
            session = message.session
            user_memory = self.get_or_create_user_memory(session.user_id, session.database_role_id)
            database_memory = self.get_or_create_database_memory(session.database_role_id)
            
            # Analyze the interaction for memory updates
            memory_updates = self._analyze_interaction_for_memory_updates(message)
            
            # Apply updates to user memory
            user_updates = self._apply_user_memory_updates(user_memory, memory_updates['user_updates'])
            
            # Apply updates to database memory
            db_updates = self._apply_database_memory_updates(database_memory, memory_updates['database_updates'])
            
            # Record memory updates
            for update in user_updates + db_updates:
                memory_update = MemoryUpdate(
                    user_memory_id=user_memory.id if update['type'] == 'user' else None,
                    database_memory_id=database_memory.id if update['type'] == 'database' else None,
                    update_type=update['update_type'],
                    update_action=update['action'],
                    old_content=update.get('old_content'),
                    new_content=update['new_content'],
                    update_reason=update['reason'],
                    triggered_by_message_id=message.id
                )
                db.session.add(memory_update)
            
            db.session.commit()
            
            return {
                'success': True,
                'user_updates': len(user_updates),
                'database_updates': len(db_updates),
                'total_updates': len(user_updates) + len(db_updates)
            }
            
        except Exception as e:
            logger.error(f"Error updating memory from interaction: {str(e)}")
            logger.error(traceback.format_exc())
            db.session.rollback()
            return {
                'success': False,
                'error': str(e)
            }
    
    def _create_initial_database_core_memory(self, role: DatabaseRole) -> Dict[str, Any]:
        """Create initial core memory for a database role"""
        try:
            return {
                'database_info': {
                    'name': role.database.name,
                    'type': role.database.db_type,
                    'role_name': role.name,
                    'role_description': role.description
                },
                'access_restrictions': {
                    'where_conditions': role.where_conditions,
                    'allowed_tables': role.allowed_tables,
                    'restricted_tables': role.restricted_tables,
                    'has_column_masking': bool(role.column_masking_rules)
                },
                'usage_patterns': {
                    'common_queries': [],
                    'frequent_tables': [],
                    'typical_filters': []
                },
                'data_insights': {
                    'key_metrics': [],
                    'important_relationships': [],
                    'data_quality_notes': []
                }
            }
            
        except Exception as e:
            logger.error(f"Error creating initial database core memory: {str(e)}")
            return {}
    
    def _create_initial_user_core_memory(self, user: User, database_role_id: int) -> Dict[str, Any]:
        """Create initial core memory for a user"""
        try:
            return {
                'user_info': {
                    'user_name': user.user_name,
                    'user_id': user.id,
                    'role_id': database_role_id
                },
                'preferences': {
                    'preferred_visualization_types': [],
                    'typical_query_patterns': [],
                    'communication_style': 'professional'
                },
                'expertise_level': {
                    'sql_knowledge': 'unknown',
                    'domain_expertise': 'unknown',
                    'data_analysis_skills': 'unknown'
                },
                'context': {
                    'current_projects': [],
                    'frequent_questions': [],
                    'important_metrics': []
                }
            }
            
        except Exception as e:
            logger.error(f"Error creating initial user core memory: {str(e)}")
            return {}
    
    def _analyze_interaction_for_memory_updates(self, message: EnhancedDatabaseChatMessage) -> Dict[str, Any]:
        """Analyze a chat interaction to determine what memory updates are needed"""
        try:
            prompt = f"""
Analyze this database chat interaction and determine what should be remembered for future conversations.

User Question: "{message.question}"
System Answer: "{message.answer}"
SQL Query: "{message.final_sql_query or 'None'}"
Query Results Count: {len(message.query_results or [])}

Determine what should be updated in memory:

1. User Memory Updates:
   - User preferences or patterns
   - Expertise level indicators
   - Important context or projects
   - Communication style

2. Database Memory Updates:
   - Common query patterns
   - Frequently accessed tables
   - Important data insights
   - Usage patterns

Return JSON:
{{
    "user_updates": [
        {{
            "type": "preference|expertise|context",
            "action": "add|update|remove",
            "content": "what to remember",
            "reason": "why this should be remembered"
        }}
    ],
    "database_updates": [
        {{
            "type": "usage_pattern|data_insight|query_pattern",
            "action": "add|update|remove", 
            "content": "what to remember",
            "reason": "why this should be remembered"
        }}
    ]
}}
"""
            
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a memory management expert. Analyze interactions and determine what should be remembered. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.3
            )
            
            analysis_text = response.choices[0].message.content.strip()
            analysis = json.loads(analysis_text)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing interaction for memory updates: {str(e)}")
            return {
                'user_updates': [],
                'database_updates': []
            }
    
    def _apply_user_memory_updates(self, user_memory: UserMemoryContext, 
                                  updates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply updates to user memory context"""
        try:
            applied_updates = []
            
            for update in updates:
                update_type = update.get('type')
                action = update.get('action')
                content = update.get('content')
                reason = update.get('reason')
                
                if update_type == 'preference':
                    old_prefs = user_memory.core_memory.get('preferences', {}).copy()
                    self._update_user_preferences(user_memory, content, action)
                    
                    applied_updates.append({
                        'type': 'user',
                        'update_type': 'preference',
                        'action': action,
                        'old_content': old_prefs,
                        'new_content': content,
                        'reason': reason
                    })
                
                elif update_type == 'expertise':
                    old_expertise = user_memory.core_memory.get('expertise_level', {}).copy()
                    self._update_user_expertise(user_memory, content, action)
                    
                    applied_updates.append({
                        'type': 'user',
                        'update_type': 'expertise',
                        'action': action,
                        'old_content': old_expertise,
                        'new_content': content,
                        'reason': reason
                    })
                
                elif update_type == 'context':
                    old_context = user_memory.core_memory.get('context', {}).copy()
                    self._update_user_context(user_memory, content, action)
                    
                    applied_updates.append({
                        'type': 'user',
                        'update_type': 'context',
                        'action': action,
                        'old_content': old_context,
                        'new_content': content,
                        'reason': reason
                    })
            
            # Update memory version and timestamp
            user_memory.memory_version += 1
            user_memory.updated_at = datetime.utcnow()
            user_memory.last_updated_by_llm = True
            
            return applied_updates
            
        except Exception as e:
            logger.error(f"Error applying user memory updates: {str(e)}")
            return []
    
    def _apply_database_memory_updates(self, database_memory: DatabaseMemoryContext,
                                     updates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Apply updates to database memory context"""
        try:
            applied_updates = []
            
            for update in updates:
                update_type = update.get('type')
                action = update.get('action')
                content = update.get('content')
                reason = update.get('reason')
                
                if update_type == 'usage_pattern':
                    old_patterns = database_memory.core_memory.get('usage_patterns', {}).copy()
                    self._update_usage_patterns(database_memory, content, action)
                    
                    applied_updates.append({
                        'type': 'database',
                        'update_type': 'usage_pattern',
                        'action': action,
                        'old_content': old_patterns,
                        'new_content': content,
                        'reason': reason
                    })
                
                elif update_type == 'data_insight':
                    old_insights = database_memory.core_memory.get('data_insights', {}).copy()
                    self._update_data_insights(database_memory, content, action)
                    
                    applied_updates.append({
                        'type': 'database',
                        'update_type': 'data_insight',
                        'action': action,
                        'old_content': old_insights,
                        'new_content': content,
                        'reason': reason
                    })
            
            # Update memory version and timestamp
            database_memory.memory_version += 1
            database_memory.updated_at = datetime.utcnow()
            database_memory.last_updated_by_llm = True
            
            return applied_updates
            
        except Exception as e:
            logger.error(f"Error applying database memory updates: {str(e)}")
            return []
    
    def _update_user_preferences(self, user_memory: UserMemoryContext, content: str, action: str):
        """Update user preferences in core memory"""
        preferences = user_memory.core_memory.setdefault('preferences', {})
        
        if action == 'add':
            # Add new preference
            if 'visualization' in content.lower():
                viz_prefs = preferences.setdefault('preferred_visualization_types', [])
                if content not in viz_prefs:
                    viz_prefs.append(content)
            elif 'query' in content.lower():
                query_prefs = preferences.setdefault('typical_query_patterns', [])
                if content not in query_prefs:
                    query_prefs.append(content)
    
    def _update_user_expertise(self, user_memory: UserMemoryContext, content: str, action: str):
        """Update user expertise level in core memory"""
        expertise = user_memory.core_memory.setdefault('expertise_level', {})
        
        if 'sql' in content.lower():
            if 'beginner' in content.lower():
                expertise['sql_knowledge'] = 'beginner'
            elif 'intermediate' in content.lower():
                expertise['sql_knowledge'] = 'intermediate'
            elif 'advanced' in content.lower():
                expertise['sql_knowledge'] = 'advanced'
    
    def _update_user_context(self, user_memory: UserMemoryContext, content: str, action: str):
        """Update user context in core memory"""
        context = user_memory.core_memory.setdefault('context', {})
        
        if action == 'add':
            if 'project' in content.lower():
                projects = context.setdefault('current_projects', [])
                if content not in projects:
                    projects.append(content)
            elif 'metric' in content.lower():
                metrics = context.setdefault('important_metrics', [])
                if content not in metrics:
                    metrics.append(content)
    
    def _update_usage_patterns(self, database_memory: DatabaseMemoryContext, content: str, action: str):
        """Update usage patterns in database memory"""
        patterns = database_memory.core_memory.setdefault('usage_patterns', {})
        
        if action == 'add':
            if 'table' in content.lower():
                tables = patterns.setdefault('frequent_tables', [])
                if content not in tables:
                    tables.append(content)
            elif 'query' in content.lower():
                queries = patterns.setdefault('common_queries', [])
                if content not in queries:
                    queries.append(content)
    
    def _update_data_insights(self, database_memory: DatabaseMemoryContext, content: str, action: str):
        """Update data insights in database memory"""
        insights = database_memory.core_memory.setdefault('data_insights', {})
        
        if action == 'add':
            if 'metric' in content.lower():
                metrics = insights.setdefault('key_metrics', [])
                if content not in metrics:
                    metrics.append(content)
            elif 'relationship' in content.lower():
                relationships = insights.setdefault('important_relationships', [])
                if content not in relationships:
                    relationships.append(content)
    
    def get_memory_context_for_chat(self, user_id: int, database_role_id: int) -> Dict[str, Any]:
        """Get formatted memory context for chat session"""
        try:
            user_memory = self.get_or_create_user_memory(user_id, database_role_id)
            database_memory = self.get_or_create_database_memory(database_role_id)
            
            return {
                'user_context': user_memory.core_memory,
                'database_context': database_memory.core_memory,
                'user_memory_id': user_memory.id,
                'database_memory_id': database_memory.id
            }
            
        except Exception as e:
            logger.error(f"Error getting memory context for chat: {str(e)}")
            return {
                'user_context': {},
                'database_context': {},
                'user_memory_id': None,
                'database_memory_id': None
            }
