"""
Database EDA (Exploratory Data Analysis) Engine

This module provides comprehensive EDA capabilities for database tables,
similar to the CSV module but optimized for database operations.
"""

import logging
import traceback
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import sqlalchemy as sa
from sqlalchemy import create_engine, text
from utils.database_utils import DatabaseConnectionManager

logger = logging.getLogger(__name__)

class DatabaseEDAEngine:
    """
    Performs comprehensive EDA on database tables
    """
    
    def __init__(self):
        self.connection_manager = DatabaseConnectionManager()
    
    def analyze_database_tables(self, db_config: Dict[str, Any], tables_info: Dict[str, Any], 
                               sample_size: int = 10000) -> Dict[str, Any]:
        """
        Perform EDA on all tables in a database
        
        Args:
            db_config: Database configuration
            tables_info: Table information from schema analysis
            sample_size: Maximum number of rows to sample for analysis
            
        Returns:
            Dictionary containing EDA results for all tables
        """
        try:
            logger.info(f"Starting EDA analysis for {len(tables_info)} tables")
            
            connection = self.connection_manager.get_connection(db_config)
            eda_results = {}
            
            try:
                for table_name, table_info in tables_info.items():
                    if table_info.get('type') == 'table':  # Skip views for now
                        logger.info(f"Analyzing table: {table_name}")
                        table_eda = self._analyze_table_eda(connection, table_name, table_info, sample_size)
                        if table_eda:
                            eda_results[table_name] = table_eda
                
                # Generate database-level summary
                database_summary = self._generate_database_summary(eda_results)
                
                return {
                    'success': True,
                    'database_summary': database_summary,
                    'table_analyses': eda_results,
                    'analysis_timestamp': datetime.utcnow().isoformat(),
                    'total_tables_analyzed': len(eda_results)
                }
                
            finally:
                connection.close()
                
        except Exception as e:
            logger.error(f"Database EDA analysis failed: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
    
    def _analyze_table_eda(self, connection, table_name: str, table_info: Dict[str, Any], 
                          sample_size: int) -> Optional[Dict[str, Any]]:
        """Perform EDA on a single table"""
        try:
            # Get table statistics
            row_count = table_info.get('statistics', {}).get('row_count', 0)
            
            if row_count == 0:
                logger.warning(f"Table {table_name} is empty, skipping EDA")
                return None
            
            # Determine sampling strategy
            if row_count > sample_size:
                # Sample data for large tables
                sample_query = self._build_sample_query(table_name, sample_size, connection)
                df = pd.read_sql(sample_query, connection)
                is_sampled = True
                logger.info(f"Sampled {len(df)} rows from {table_name} (total: {row_count})")
            else:
                # Load full table for small tables
                df = pd.read_sql(f"SELECT * FROM {table_name}", connection)
                is_sampled = False
                logger.info(f"Loaded full table {table_name} with {len(df)} rows")
            
            if df.empty:
                return None
            
            # Perform comprehensive EDA
            eda_result = {
                'table_name': table_name,
                'total_rows': row_count,
                'sampled_rows': len(df),
                'is_sampled': is_sampled,
                'columns_analyzed': len(df.columns),
                'analysis_timestamp': datetime.utcnow().isoformat()
            }
            
            # Basic statistics
            eda_result['basic_stats'] = self._get_basic_statistics(df)
            
            # Column analysis
            eda_result['column_analysis'] = self._analyze_columns(df)
            
            # Data quality metrics
            eda_result['data_quality'] = self._analyze_data_quality(df)
            
            # Correlation analysis (for numeric columns)
            eda_result['correlations'] = self._analyze_correlations(df)
            
            # Value distributions
            eda_result['distributions'] = self._analyze_distributions(df)
            
            # Outlier detection
            eda_result['outliers'] = self._detect_outliers(df)
            
            return eda_result
            
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {str(e)}")
            return None
    
    def _build_sample_query(self, table_name: str, sample_size: int, connection) -> str:
        """Build appropriate sampling query based on database type"""
        try:
            # Get database type from connection
            db_type = connection.dialect.name.lower()
            
            if db_type == 'postgresql':
                return f"SELECT * FROM {table_name} TABLESAMPLE SYSTEM(10) LIMIT {sample_size}"
            elif db_type == 'mysql':
                return f"SELECT * FROM {table_name} ORDER BY RAND() LIMIT {sample_size}"
            elif db_type == 'sqlserver':
                return f"SELECT TOP {sample_size} * FROM {table_name} ORDER BY NEWID()"
            else:
                # Fallback to LIMIT for other databases
                return f"SELECT * FROM {table_name} LIMIT {sample_size}"
                
        except Exception as e:
            logger.warning(f"Could not determine database type for sampling, using LIMIT: {str(e)}")
            return f"SELECT * FROM {table_name} LIMIT {sample_size}"
    
    def _get_basic_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Get basic statistical information about the dataframe"""
        try:
            stats = {
                'shape': df.shape,
                'memory_usage_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
                'dtypes': df.dtypes.astype(str).to_dict()
            }
            
            # Numeric columns statistics
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            if numeric_cols:
                numeric_stats = df[numeric_cols].describe().to_dict()
                stats['numeric_summary'] = numeric_stats
                stats['numeric_columns'] = numeric_cols
            
            # Text columns statistics
            text_cols = df.select_dtypes(include=['object', 'string']).columns.tolist()
            if text_cols:
                text_stats = {}
                for col in text_cols:
                    text_stats[col] = {
                        'unique_count': df[col].nunique(),
                        'most_common': df[col].value_counts().head(5).to_dict(),
                        'avg_length': df[col].astype(str).str.len().mean() if not df[col].empty else 0
                    }
                stats['text_summary'] = text_stats
                stats['text_columns'] = text_cols
            
            # Date columns
            date_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
            if date_cols:
                date_stats = {}
                for col in date_cols:
                    date_stats[col] = {
                        'min_date': df[col].min().isoformat() if pd.notna(df[col].min()) else None,
                        'max_date': df[col].max().isoformat() if pd.notna(df[col].max()) else None,
                        'date_range_days': (df[col].max() - df[col].min()).days if pd.notna(df[col].min()) and pd.notna(df[col].max()) else None
                    }
                stats['date_summary'] = date_stats
                stats['date_columns'] = date_cols
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting basic statistics: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_columns(self, df: pd.DataFrame) -> Dict[str, Dict[str, Any]]:
        """Analyze each column individually"""
        try:
            column_analysis = {}
            
            for col in df.columns:
                col_data = df[col]
                
                analysis = {
                    'dtype': str(col_data.dtype),
                    'non_null_count': col_data.count(),
                    'null_count': col_data.isnull().sum(),
                    'null_percentage': (col_data.isnull().sum() / len(col_data)) * 100,
                    'unique_count': col_data.nunique(),
                    'unique_percentage': (col_data.nunique() / len(col_data)) * 100 if len(col_data) > 0 else 0
                }
                
                # Type-specific analysis
                if pd.api.types.is_numeric_dtype(col_data):
                    analysis.update(self._analyze_numeric_column(col_data))
                elif pd.api.types.is_string_dtype(col_data) or col_data.dtype == 'object':
                    analysis.update(self._analyze_text_column(col_data))
                elif pd.api.types.is_datetime64_any_dtype(col_data):
                    analysis.update(self._analyze_date_column(col_data))
                
                column_analysis[col] = analysis
            
            return column_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing columns: {str(e)}")
            return {}
    
    def _analyze_numeric_column(self, series: pd.Series) -> Dict[str, Any]:
        """Analyze numeric column"""
        try:
            stats = {
                'min': float(series.min()) if pd.notna(series.min()) else None,
                'max': float(series.max()) if pd.notna(series.max()) else None,
                'mean': float(series.mean()) if pd.notna(series.mean()) else None,
                'median': float(series.median()) if pd.notna(series.median()) else None,
                'std': float(series.std()) if pd.notna(series.std()) else None,
                'skewness': float(series.skew()) if pd.notna(series.skew()) else None,
                'kurtosis': float(series.kurtosis()) if pd.notna(series.kurtosis()) else None
            }
            
            # Quartiles
            try:
                quartiles = series.quantile([0.25, 0.5, 0.75]).to_dict()
                stats['quartiles'] = {f'q{int(k*100)}': float(v) for k, v in quartiles.items()}
            except:
                pass
            
            # Zero and negative counts
            stats['zero_count'] = int((series == 0).sum())
            stats['negative_count'] = int((series < 0).sum())
            stats['positive_count'] = int((series > 0).sum())
            
            return stats
            
        except Exception as e:
            logger.error(f"Error analyzing numeric column: {str(e)}")
            return {}
    
    def _analyze_text_column(self, series: pd.Series) -> Dict[str, Any]:
        """Analyze text column"""
        try:
            # Convert to string to handle mixed types
            str_series = series.astype(str)
            
            stats = {
                'avg_length': float(str_series.str.len().mean()),
                'min_length': int(str_series.str.len().min()),
                'max_length': int(str_series.str.len().max()),
                'empty_string_count': int((str_series == '').sum()),
                'most_common_values': series.value_counts().head(10).to_dict()
            }
            
            # Pattern analysis
            stats['contains_numbers'] = int(str_series.str.contains(r'\d', na=False).sum())
            stats['contains_special_chars'] = int(str_series.str.contains(r'[^a-zA-Z0-9\s]', na=False).sum())
            stats['all_uppercase'] = int(str_series.str.isupper().sum())
            stats['all_lowercase'] = int(str_series.str.islower().sum())
            
            return stats
            
        except Exception as e:
            logger.error(f"Error analyzing text column: {str(e)}")
            return {}
    
    def _analyze_date_column(self, series: pd.Series) -> Dict[str, Any]:
        """Analyze date column"""
        try:
            stats = {
                'min_date': series.min().isoformat() if pd.notna(series.min()) else None,
                'max_date': series.max().isoformat() if pd.notna(series.max()) else None,
            }
            
            if pd.notna(series.min()) and pd.notna(series.max()):
                stats['date_range_days'] = (series.max() - series.min()).days
            
            # Extract date components for analysis
            stats['year_range'] = [int(series.dt.year.min()), int(series.dt.year.max())] if pd.notna(series.min()) else None
            stats['month_distribution'] = series.dt.month.value_counts().to_dict()
            stats['day_of_week_distribution'] = series.dt.dayofweek.value_counts().to_dict()
            
            return stats
            
        except Exception as e:
            logger.error(f"Error analyzing date column: {str(e)}")
            return {}
    
    def _analyze_data_quality(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze data quality metrics"""
        try:
            quality_metrics = {
                'total_cells': df.size,
                'missing_cells': df.isnull().sum().sum(),
                'missing_percentage': (df.isnull().sum().sum() / df.size) * 100,
                'duplicate_rows': df.duplicated().sum(),
                'duplicate_percentage': (df.duplicated().sum() / len(df)) * 100,
                'columns_with_missing': df.isnull().any().sum(),
                'completely_empty_columns': (df.isnull().all()).sum()
            }
            
            # Missing data by column
            missing_by_column = df.isnull().sum()
            quality_metrics['missing_by_column'] = missing_by_column[missing_by_column > 0].to_dict()
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"Error analyzing data quality: {str(e)}")
            return {}
    
    def _analyze_correlations(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze correlations between numeric columns"""
        try:
            numeric_df = df.select_dtypes(include=[np.number])
            
            if numeric_df.shape[1] < 2:
                return {'message': 'Not enough numeric columns for correlation analysis'}
            
            # Calculate correlation matrix
            corr_matrix = numeric_df.corr()
            
            # Find strong correlations (> 0.7 or < -0.7)
            strong_correlations = []
            for i in range(len(corr_matrix.columns)):
                for j in range(i+1, len(corr_matrix.columns)):
                    corr_value = corr_matrix.iloc[i, j]
                    if abs(corr_value) > 0.7:
                        strong_correlations.append({
                            'column1': corr_matrix.columns[i],
                            'column2': corr_matrix.columns[j],
                            'correlation': float(corr_value)
                        })
            
            return {
                'correlation_matrix': corr_matrix.to_dict(),
                'strong_correlations': strong_correlations,
                'numeric_columns_count': len(numeric_df.columns)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing correlations: {str(e)}")
            return {}
    
    def _analyze_distributions(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze value distributions"""
        try:
            distributions = {}
            
            for col in df.columns:
                if df[col].dtype in ['object', 'string'] and df[col].nunique() <= 50:
                    # Categorical distribution
                    value_counts = df[col].value_counts().head(20)
                    distributions[col] = {
                        'type': 'categorical',
                        'distribution': value_counts.to_dict(),
                        'unique_values': df[col].nunique()
                    }
                elif pd.api.types.is_numeric_dtype(df[col]):
                    # Numeric distribution (histogram bins)
                    try:
                        hist, bin_edges = np.histogram(df[col].dropna(), bins=20)
                        distributions[col] = {
                            'type': 'numeric',
                            'histogram': {
                                'counts': hist.tolist(),
                                'bin_edges': bin_edges.tolist()
                            }
                        }
                    except:
                        pass
            
            return distributions
            
        except Exception as e:
            logger.error(f"Error analyzing distributions: {str(e)}")
            return {}
    
    def _detect_outliers(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Detect outliers in numeric columns"""
        try:
            outliers = {}
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            
            for col in numeric_cols:
                series = df[col].dropna()
                if len(series) == 0:
                    continue
                
                # IQR method
                Q1 = series.quantile(0.25)
                Q3 = series.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                outlier_mask = (series < lower_bound) | (series > upper_bound)
                outlier_count = outlier_mask.sum()
                
                if outlier_count > 0:
                    outliers[col] = {
                        'count': int(outlier_count),
                        'percentage': float((outlier_count / len(series)) * 100),
                        'lower_bound': float(lower_bound),
                        'upper_bound': float(upper_bound),
                        'outlier_values': series[outlier_mask].head(10).tolist()
                    }
            
            return outliers
            
        except Exception as e:
            logger.error(f"Error detecting outliers: {str(e)}")
            return {}
    
    def _generate_database_summary(self, table_analyses: Dict[str, Any]) -> Dict[str, Any]:
        """Generate database-level summary from table analyses"""
        try:
            if not table_analyses:
                return {}
            
            total_rows = sum(analysis.get('total_rows', 0) for analysis in table_analyses.values())
            total_columns = sum(analysis.get('columns_analyzed', 0) for analysis in table_analyses.values())
            
            # Aggregate data quality metrics
            total_missing_percentage = np.mean([
                analysis.get('data_quality', {}).get('missing_percentage', 0) 
                for analysis in table_analyses.values()
            ])
            
            # Find tables with data quality issues
            problematic_tables = []
            for table_name, analysis in table_analyses.items():
                missing_pct = analysis.get('data_quality', {}).get('missing_percentage', 0)
                duplicate_pct = analysis.get('data_quality', {}).get('duplicate_percentage', 0)
                
                if missing_pct > 20 or duplicate_pct > 10:
                    problematic_tables.append({
                        'table': table_name,
                        'missing_percentage': missing_pct,
                        'duplicate_percentage': duplicate_pct
                    })
            
            summary = {
                'total_tables_analyzed': len(table_analyses),
                'total_rows': total_rows,
                'total_columns': total_columns,
                'average_missing_percentage': total_missing_percentage,
                'problematic_tables': problematic_tables,
                'analysis_completion_rate': 100.0  # All requested tables were analyzed
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating database summary: {str(e)}")
            return {}
