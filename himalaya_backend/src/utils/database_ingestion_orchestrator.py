"""
Database Ingestion Orchestrator

This module orchestrates the complete database ingestion pipeline:
1. Connection testing
2. Schema analysis
3. EDA (Exploratory Data Analysis)
4. Semantic summary generation
5. Data storage in application database
"""

import logging
import traceback
from typing import Dict, Any, Optional
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError

from models.models import (
    db, ExternalDatabase, DatabaseSchema, DatabaseTable, DatabaseEDA, 
    DatabaseSemanticSummary, DatabaseIngestionJob
)
from utils.database_schema_analyzer import DatabaseSchemaAnalyzer
from utils.database_eda_engine import DatabaseEDAEngine
from utils.database_semantic_generator import DatabaseSemanticGenerator

logger = logging.getLogger(__name__)

class DatabaseIngestionOrchestrator:
    """
    Orchestrates the complete database ingestion process
    """
    
    def __init__(self):
        self.schema_analyzer = DatabaseSchemaAnalyzer()
        self.eda_engine = DatabaseEDAEngine()
        self.semantic_generator = DatabaseSemanticGenerator()
    
    def start_full_ingestion(self, external_database_id: int, user_id: int, 
                           job_type: str = 'full_ingestion') -> Dict[str, Any]:
        """
        Start a complete database ingestion job
        
        Args:
            external_database_id: ID of the external database to ingest
            user_id: ID of the user starting the job
            job_type: Type of ingestion job
            
        Returns:
            Dictionary containing job status and details
        """
        try:
            # Get database configuration
            external_db = ExternalDatabase.query.get(external_database_id)
            if not external_db:
                return {
                    'success': False,
                    'error': 'Database not found',
                    'job_id': None
                }
            
            # Create ingestion job record
            job = DatabaseIngestionJob(
                external_database_id=external_database_id,
                job_type=job_type,
                status='pending',
                started_by=user_id,
                current_step='Initializing'
            )
            db.session.add(job)
            db.session.commit()
            
            logger.info(f"Started ingestion job {job.id} for database {external_db.name}")
            
            # Start the ingestion process
            result = self._execute_ingestion_pipeline(job, external_db)
            
            return {
                'success': result['success'],
                'job_id': job.id,
                'message': result.get('message', 'Ingestion completed'),
                'error': result.get('error'),
                'steps_completed': result.get('steps_completed', [])
            }
            
        except Exception as e:
            logger.error(f"Error starting ingestion job: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': str(e),
                'job_id': None
            }
    
    def _execute_ingestion_pipeline(self, job: DatabaseIngestionJob, 
                                  external_db: ExternalDatabase) -> Dict[str, Any]:
        """Execute the complete ingestion pipeline"""
        try:
            steps_completed = []
            
            # Prepare database configuration
            db_config = {
                'name': external_db.name,
                'db_type': external_db.db_type,
                'host': external_db.host,
                'port': external_db.port,
                'database_name': external_db.database_name,
                'username': external_db.username,
                'password_encrypted': external_db.password_encrypted,
                'ssl_enabled': external_db.ssl_enabled,
                'additional_params': external_db.additional_params or {}
            }
            
            # Step 1: Schema Analysis
            job.current_step = 'Schema Analysis'
            job.status = 'running'
            db.session.commit()
            
            logger.info(f"Job {job.id}: Starting schema analysis")
            schema_result = self.schema_analyzer.analyze_database_schema(db_config)
            
            if not schema_result.get('success'):
                job.status = 'failed'
                job.error_message = f"Schema analysis failed: {schema_result.get('error')}"
                db.session.commit()
                return {
                    'success': False,
                    'error': job.error_message,
                    'steps_completed': steps_completed
                }
            
            # Store schema analysis results
            self._store_schema_analysis(external_db.id, schema_result)
            job.schema_analysis_completed = True
            job.total_tables = len(schema_result.get('tables', {}))
            steps_completed.append('Schema Analysis')
            db.session.commit()
            
            logger.info(f"Job {job.id}: Schema analysis completed, found {job.total_tables} tables")
            
            # Step 2: EDA Analysis
            if job.job_type in ['full_ingestion', 'eda_only']:
                job.current_step = 'EDA Analysis'
                db.session.commit()
                
                logger.info(f"Job {job.id}: Starting EDA analysis")
                eda_result = self.eda_engine.analyze_database_tables(
                    db_config, schema_result.get('tables', {}), sample_size=10000
                )
                
                if eda_result.get('success'):
                    # Store EDA results
                    self._store_eda_analysis(external_db.id, eda_result)
                    job.eda_completed = True
                    steps_completed.append('EDA Analysis')
                    logger.info(f"Job {job.id}: EDA analysis completed")
                else:
                    logger.warning(f"Job {job.id}: EDA analysis failed: {eda_result.get('error')}")
                    eda_result = {}  # Continue without EDA
                
                db.session.commit()
            else:
                eda_result = {}
            
            # Step 3: Semantic Summary Generation
            if job.job_type in ['full_ingestion']:
                job.current_step = 'Semantic Summary Generation'
                db.session.commit()
                
                logger.info(f"Job {job.id}: Starting semantic summary generation")
                
                # Generate database-level summary
                db_summary = self.semantic_generator.generate_database_summary(
                    db_config, schema_result, eda_result
                )
                
                # Generate table-level summaries
                table_summaries = self.semantic_generator.generate_table_summaries(
                    db_config, schema_result, eda_result
                )
                
                # Store semantic summaries
                self._store_semantic_summaries(external_db.id, db_summary, table_summaries)
                job.summary_generation_completed = True
                steps_completed.append('Semantic Summary Generation')
                
                logger.info(f"Job {job.id}: Semantic summary generation completed")
                db.session.commit()
            
            # Complete the job
            job.status = 'completed'
            job.completed_at = datetime.utcnow()
            job.current_step = 'Completed'
            db.session.commit()
            
            logger.info(f"Job {job.id}: Ingestion pipeline completed successfully")
            
            return {
                'success': True,
                'message': 'Database ingestion completed successfully',
                'steps_completed': steps_completed
            }
            
        except Exception as e:
            logger.error(f"Error in ingestion pipeline: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Update job status
            job.status = 'failed'
            job.error_message = str(e)
            job.error_details = {'traceback': traceback.format_exc()}
            db.session.commit()
            
            return {
                'success': False,
                'error': str(e),
                'steps_completed': steps_completed
            }
    
    def _store_schema_analysis(self, external_database_id: int, schema_result: Dict[str, Any]):
        """Store schema analysis results in the database"""
        try:
            # Store main schema record
            schema_record = DatabaseSchema(
                external_database_id=external_database_id,
                schema_data=schema_result,
                table_count=len(schema_result.get('tables', {})),
                column_count=schema_result.get('total_columns', 0),
                analysis_status='completed',
                last_analyzed_at=datetime.utcnow()
            )
            db.session.add(schema_record)
            db.session.flush()  # Get the ID
            
            # Store individual table records
            tables_info = schema_result.get('tables', {})
            for table_name, table_info in tables_info.items():
                table_record = DatabaseTable(
                    database_schema_id=schema_record.id,
                    table_name=table_name,
                    table_type=table_info.get('type', 'table'),
                    row_count=table_info.get('statistics', {}).get('row_count'),
                    column_count=len(table_info.get('columns', [])),
                    columns_data=table_info
                )
                db.session.add(table_record)
            
            db.session.commit()
            logger.info(f"Stored schema analysis for {len(tables_info)} tables")
            
        except Exception as e:
            logger.error(f"Error storing schema analysis: {str(e)}")
            db.session.rollback()
            raise
    
    def _store_eda_analysis(self, external_database_id: int, eda_result: Dict[str, Any]):
        """Store EDA analysis results in the database"""
        try:
            # Store database-level EDA
            db_eda = DatabaseEDA(
                external_database_id=external_database_id,
                table_id=None,  # Database-level
                eda_type='database',
                eda_data=eda_result.get('database_summary', {}),
                analysis_status='completed'
            )
            db.session.add(db_eda)
            
            # Store table-level EDA results
            table_analyses = eda_result.get('table_analyses', {})
            for table_name, table_eda in table_analyses.items():
                # Find the corresponding table record
                table_record = DatabaseTable.query.join(DatabaseSchema).filter(
                    DatabaseSchema.external_database_id == external_database_id,
                    DatabaseTable.table_name == table_name
                ).first()
                
                if table_record:
                    table_eda_record = DatabaseEDA(
                        external_database_id=external_database_id,
                        table_id=table_record.id,
                        eda_type='table',
                        eda_data=table_eda,
                        statistical_summary=table_eda.get('basic_stats', {}),
                        data_quality_metrics=table_eda.get('data_quality', {}),
                        correlation_matrix=table_eda.get('correlations', {}),
                        analysis_status='completed'
                    )
                    db.session.add(table_eda_record)
            
            db.session.commit()
            logger.info(f"Stored EDA analysis for {len(table_analyses)} tables")
            
        except Exception as e:
            logger.error(f"Error storing EDA analysis: {str(e)}")
            db.session.rollback()
            raise
    
    def _store_semantic_summaries(self, external_database_id: int, db_summary: Dict[str, Any], 
                                table_summaries: Dict[str, Dict[str, Any]]):
        """Store semantic summaries in the database"""
        try:
            # Store database-level summary
            if db_summary.get('success'):
                db_summary_record = DatabaseSemanticSummary(
                    external_database_id=external_database_id,
                    table_id=None,
                    summary_type='database',
                    target_name=db_summary.get('target_name', 'Database'),
                    llm_generated_summary=db_summary.get('llm_generated_summary', ''),
                    current_summary=db_summary.get('current_summary', ''),
                    token_count=db_summary.get('token_count', 0),
                    created_by=1,  # System user - should be parameterized
                    is_active=True
                )
                db.session.add(db_summary_record)
            
            # Store table-level summaries
            for table_name, table_summary in table_summaries.items():
                if table_summary.get('success'):
                    # Find the corresponding table record
                    table_record = DatabaseTable.query.join(DatabaseSchema).filter(
                        DatabaseSchema.external_database_id == external_database_id,
                        DatabaseTable.table_name == table_name
                    ).first()
                    
                    if table_record:
                        table_summary_record = DatabaseSemanticSummary(
                            external_database_id=external_database_id,
                            table_id=table_record.id,
                            summary_type='table',
                            target_name=table_name,
                            llm_generated_summary=table_summary.get('llm_generated_summary', ''),
                            current_summary=table_summary.get('current_summary', ''),
                            token_count=table_summary.get('token_count', 0),
                            created_by=1,  # System user - should be parameterized
                            is_active=True
                        )
                        db.session.add(table_summary_record)
            
            db.session.commit()
            logger.info(f"Stored semantic summaries for database and {len(table_summaries)} tables")
            
        except Exception as e:
            logger.error(f"Error storing semantic summaries: {str(e)}")
            db.session.rollback()
            raise
    
    def get_ingestion_status(self, job_id: int) -> Dict[str, Any]:
        """Get the status of an ingestion job"""
        try:
            job = DatabaseIngestionJob.query.get(job_id)
            if not job:
                return {
                    'success': False,
                    'error': 'Job not found'
                }
            
            return {
                'success': True,
                'job_id': job.id,
                'status': job.status,
                'current_step': job.current_step,
                'total_tables': job.total_tables,
                'processed_tables': job.processed_tables,
                'schema_completed': job.schema_analysis_completed,
                'eda_completed': job.eda_completed,
                'summaries_completed': job.summary_generation_completed,
                'started_at': job.started_at.isoformat() if job.started_at else None,
                'completed_at': job.completed_at.isoformat() if job.completed_at else None,
                'error_message': job.error_message
            }
            
        except Exception as e:
            logger.error(f"Error getting ingestion status: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def retry_failed_job(self, job_id: int, user_id: int) -> Dict[str, Any]:
        """Retry a failed ingestion job"""
        try:
            job = DatabaseIngestionJob.query.get(job_id)
            if not job:
                return {
                    'success': False,
                    'error': 'Job not found'
                }
            
            if job.status != 'failed':
                return {
                    'success': False,
                    'error': 'Job is not in failed state'
                }
            
            # Reset job status
            job.status = 'pending'
            job.current_step = 'Retrying'
            job.error_message = None
            job.error_details = None
            db.session.commit()
            
            # Get database and restart pipeline
            external_db = ExternalDatabase.query.get(job.external_database_id)
            result = self._execute_ingestion_pipeline(job, external_db)
            
            return result
            
        except Exception as e:
            logger.error(f"Error retrying job: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
