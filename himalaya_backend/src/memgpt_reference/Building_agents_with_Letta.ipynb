{"cells": [{"cell_type": "markdown", "id": "7b8b601792ff9a31", "metadata": {}, "source": ["# Lab 3: Building Agents with memory"]}, {"cell_type": "markdown", "id": "2578f35eeb7cdf49", "metadata": {}, "source": ["## Preparation\n", "\n", "<div style=\"background-color:#fff6ff; padding:13px; border-width:3px; border-color:#efe6ef; border-style:solid; border-radius:6px\">\n", "<p> 💻 &nbsp; <b>Access <code>requirements.txt</code> and <code>helper.py</code> files:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Open\"</em>.\n", "\n", "<p> ⬇ &nbsp; <b>Download Notebooks:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Download as\"</em> and select <em>\"Notebook (.ipynb)\"</em>.</p>\n", "\n", "<p> 📒 &nbsp; For more help, please see the <em>\"Appendix – Tips, Help, and Download\"</em> Lesson.</p>\n", "</div>"]}, {"cell_type": "markdown", "id": "3219d6d38bfeb902", "metadata": {}, "source": ["<p style=\"background-color:#f7fff8; padding:15px; border-width:3px; border-color:#e0f0e0; border-style:solid; border-radius:6px\"> 🚨\n", "&nbsp; <b>Different Run Results:</b> The output generated by AI models can vary with each execution due to their dynamic, probabilistic nature. Your results may differ from those shown in the video.</p>"]}, {"cell_type": "markdown", "id": "6abfbb1d-11a7-4869-bc8e-e72c8a45a3c7", "metadata": {}, "source": ["## Section 0: Setup a Letta client"]}, {"cell_type": "code", "execution_count": null, "id": "92d55f84-5e76-4a58-94bc-d7885de30f94", "metadata": {"height": 81}, "outputs": [], "source": ["from letta_client import Letta\n", "\n", "client = Letta(base_url=\"http://localhost:8283\")\n", "# client = Letta(token=\"LETTA_API_KEY\")"]}, {"cell_type": "code", "execution_count": null, "id": "c63e4128-e26c-4ab7-a558-bc2936e9e76f", "metadata": {"height": 200}, "outputs": [], "source": ["def print_message(message):\n", "    if message.message_type == \"reasoning_message\":\n", "        print(\"🧠 Reasoning: \" + message.reasoning)\n", "    elif message.message_type == \"assistant_message\":\n", "        print(\"🤖 Agent: \" + message.content)\n", "    elif message.message_type == \"tool_call_message\":\n", "        print(\"🔧 Tool Call: \" + message.tool_call.name + \"\\n\" + message.tool_call.arguments)\n", "    elif message.message_type == \"tool_return_message\":\n", "        print(\"🔧 Tool Return: \" + message.tool_return)\n", "    elif message.message_type == \"user_message\":\n", "        print(\"👤 User Message: \" + message.content)"]}, {"cell_type": "markdown", "id": "9b31eeaa-3f97-48d1-9893-ac02d89bad5b", "metadata": {}, "source": ["## Section 1: Creating a simple agent with memory"]}, {"cell_type": "markdown", "id": "6d47a3631cacb49f", "metadata": {}, "source": ["### Creating an agent"]}, {"cell_type": "code", "execution_count": null, "id": "c466117f-13d6-48bd-af0b-b66e8a6b89e3", "metadata": {"height": 285}, "outputs": [], "source": ["agent_state = client.agents.create(\n", "    name=\"simple_agent\",\n", "    memory_blocks=[\n", "        {\n", "          \"label\": \"human\",\n", "          \"value\": \"My name is <PERSON>\",\n", "          \"limit\": 10000 # character limit\n", "        },\n", "        {\n", "          \"label\": \"persona\",\n", "          \"value\": \"You are a helpful assistant and you always use emojis\"\n", "        }\n", "    ],\n", "    model=\"openai/gpt-4o-mini-2024-07-18\",\n", "    embedding=\"openai/text-embedding-3-small\"\n", ")"]}, {"cell_type": "markdown", "id": "6b208f49ff93d267", "metadata": {}, "source": ["### Messaging an agent"]}, {"cell_type": "code", "execution_count": null, "id": "f66fe93a-dd4b-4a5a-92d2-d8e5dd957e44", "metadata": {"height": 251}, "outputs": [], "source": ["# send a message to the agent\n", "response = client.agents.messages.create(\n", "    agent_id=agent_state.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"hows it going????\"\n", "        }\n", "    ]\n", ")\n", "\n", "# if we want to print the messages\n", "for message in response.messages:\n", "    print_message(message)"]}, {"cell_type": "markdown", "id": "8e6a222557891885", "metadata": {}, "source": ["### Viewing usage information"]}, {"cell_type": "code", "execution_count": null, "id": "8ec605ed-543c-41ec-9ec2-63e0b10f7ee0", "metadata": {"height": 81}, "outputs": [], "source": ["# if we want to print the usage stats\n", "print(response.usage.completion_tokens)\n", "print(response.usage.prompt_tokens)\n", "print(response.usage.step_count)"]}, {"cell_type": "markdown", "id": "c84a8ac4fadc1a9c", "metadata": {}, "source": ["### Understanding agent state"]}, {"cell_type": "code", "execution_count": null, "id": "0fbe1bc6-ba1e-49f5-8b7f-629113b63cd3", "metadata": {"height": 30}, "outputs": [], "source": ["print(agent_state.system)"]}, {"cell_type": "code", "execution_count": null, "id": "592867c7-0037-4be3-b08c-6cede8d8bb19", "metadata": {"height": 30}, "outputs": [], "source": ["[t.name for t in agent_state.tools]"]}, {"cell_type": "markdown", "id": "1442e292326649b1", "metadata": {}, "source": ["📝 Note: Memory blocks are returned as an unordered list and you may receive blocks in an order different than in the video"]}, {"cell_type": "code", "execution_count": null, "id": "a6960cf9-33f7-48ba-ab31-a9f1bc90df67", "metadata": {"height": 30}, "outputs": [], "source": ["agent_state.memory"]}, {"cell_type": "markdown", "id": "a4304ba05e3b5ca1", "metadata": {}, "source": ["📝 Note: Returned messages may differ slightly from those in the video"]}, {"cell_type": "code", "execution_count": null, "id": "59de4aa6-7bc4-489c-8f5e-67a3e033b89f", "metadata": {"height": 47}, "outputs": [], "source": ["for message in client.agents.messages.list(agent_id=agent_state.id):\n", "    print_message(message)"]}, {"cell_type": "code", "execution_count": null, "id": "f55d0a14-cbcf-4fa6-a777-bf5dd4b97df2", "metadata": {"height": 81}, "outputs": [], "source": ["passages = client.agents.passages.list(\n", "    agent_id=agent_state.id,\n", ")\n", "passages"]}, {"cell_type": "markdown", "id": "785afb072eb38e0e", "metadata": {}, "source": ["## Section 2: Understanding core memory"]}, {"cell_type": "markdown", "id": "6dd5f895ab66e11", "metadata": {}, "source": ["### Giving agent new information"]}, {"cell_type": "code", "execution_count": null, "id": "9a898740d634dee7", "metadata": {"height": 251}, "outputs": [], "source": ["# send a message to the agent\n", "response = client.agents.messages.create(\n", "    agent_id=agent_state.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"my name actually <PERSON> \"\n", "        }\n", "    ]\n", ")\n", "\n", "# if we want to print the messages\n", "for message in response.messages:\n", "    print_message(message)"]}, {"cell_type": "code", "execution_count": null, "id": "3dd6c2f5-d648-4cc2-9e97-21964b2d6f54", "metadata": {"height": 64}, "outputs": [], "source": ["print(response.usage.completion_tokens)\n", "print(response.usage.prompt_tokens)\n", "print(response.usage.step_count)"]}, {"cell_type": "markdown", "id": "909f2949567b1e42", "metadata": {}, "source": ["### Retrieving new values"]}, {"cell_type": "code", "execution_count": null, "id": "2adb0001-6930-48e9-b973-05f7640c4388", "metadata": {"height": 81}, "outputs": [], "source": ["client.agents.blocks.retrieve(\n", "    agent_id=agent_state.id,\n", "    block_label=\"human\"\n", ").value"]}, {"cell_type": "markdown", "id": "1c9d2247c5711249", "metadata": {}, "source": ["## Section 3: Understanding archival memory"]}, {"cell_type": "markdown", "id": "c0ac7349b385eafc", "metadata": {}, "source": ["### Saving information to archival memory"]}, {"cell_type": "code", "execution_count": null, "id": "9b69babe-0b75-4b0f-b5ce-796c4ec037cb", "metadata": {"height": 81}, "outputs": [], "source": ["passages = client.agents.passages.list(\n", "    agent_id=agent_state.id,\n", ")\n", "passages"]}, {"cell_type": "code", "execution_count": null, "id": "2cc30e24-b771-45d7-85f7-6aa37d3daf36", "metadata": {"height": 234}, "outputs": [], "source": ["response = client.agents.messages.create(\n", "    agent_id=agent_state.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Save the information that 'bob loves cats' to archival\"\n", "        }\n", "    ]\n", ")\n", "\n", "# if we want to print the messages\n", "for message in response.messages:\n", "    print_message(message)"]}, {"cell_type": "code", "execution_count": null, "id": "960b50f8-5f4b-48e6-a32a-5706f563cc5f", "metadata": {"height": 81}, "outputs": [], "source": ["passages = client.agents.passages.list(\n", "    agent_id=agent_state.id,\n", ")\n", "[passage.text for passage in passages]"]}, {"cell_type": "markdown", "id": "dbc781d4e9968e4c", "metadata": {}, "source": ["### Explicitly creating archival memories"]}, {"cell_type": "code", "execution_count": null, "id": "9de8059b-b374-4f01-b433-af200c2f74b0", "metadata": {"height": 81}, "outputs": [], "source": ["client.agents.passages.create(\n", "    agent_id=agent_state.id,\n", "    text=\"<PERSON><PERSON><PERSON> loves boston terriers\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "096cc39d-b072-4dbf-936b-8ed46203f568", "metadata": {"height": 234}, "outputs": [], "source": ["# send a message to the agent\n", "response = client.agents.messages.create(\n", "    agent_id=agent_state.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"What animals do I like? Search archival.\"\n", "        }\n", "    ]\n", ")\n", "\n", "for message in response.messages:\n", "    print_message(message)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}