{"cells": [{"cell_type": "markdown", "id": "e50d93241503e1e", "metadata": {}, "source": ["# Lab 4: Programming Agent Memory"]}, {"cell_type": "markdown", "id": "b0c628225ac6ead", "metadata": {}, "source": ["## Preparation\n", "\n", "<div style=\"background-color:#fff6ff; padding:13px; border-width:3px; border-color:#efe6ef; border-style:solid; border-radius:6px\">\n", "<p> 💻 &nbsp; <b>Access <code>requirements.txt</code> and <code>helper.py</code> files:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Open\"</em>.\n", "\n", "<p> ⬇ &nbsp; <b>Download Notebooks:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Download as\"</em> and select <em>\"Notebook (.ipynb)\"</em>.</p>\n", "\n", "<p> 📒 &nbsp; For more help, please see the <em>\"Appendix – Tips, Help, and Download\"</em> Lesson.</p>\n", "</div>"]}, {"cell_type": "markdown", "id": "ded0d05e8caa9479", "metadata": {}, "source": ["<p style=\"background-color:#f7fff8; padding:15px; border-width:3px; border-color:#e0f0e0; border-style:solid; border-radius:6px\"> 🚨\n", "&nbsp; <b>Different Run Results:</b> The output generated by AI models can vary with each execution due to their dynamic, probabilistic nature. Your results may differ from those shown in the video.</p>"]}, {"cell_type": "markdown", "id": "cac18b1d-0b02-484f-b89e-21f3630c4468", "metadata": {}, "source": ["## Section 0: Setup a Letta client"]}, {"cell_type": "code", "execution_count": null, "id": "2715cb2d-96a8-4276-a7e4-81011ba28a60", "metadata": {"height": 64}, "outputs": [], "source": ["from letta_client import Letta\n", "\n", "client = Letta(base_url=\"http://localhost:8283\")"]}, {"cell_type": "code", "execution_count": null, "id": "5ba2da84-da85-4453-91b9-f3ae2ffe01ee", "metadata": {"height": 302}, "outputs": [], "source": ["def print_message(message):  \n", "    if message.message_type == \"reasoning_message\": \n", "        print(\"🧠 Reasoning: \" + message.reasoning) \n", "    elif message.message_type == \"assistant_message\": \n", "        print(\"🤖 Agent: \" + message.content) \n", "    elif message.message_type == \"tool_call_message\": \n", "        print(\"🔧 Tool Call: \" + message.tool_call.name + \"\\n\" + message.tool_call.arguments)\n", "    elif message.message_type == \"tool_return_message\": \n", "        print(\"🔧 Tool Return: \" + message.tool_return)\n", "    elif message.message_type == \"user_message\": \n", "        print(\"👤 User Message: \" + message.content)\n", "    elif message.message_type == \"usage_statistics\": \n", "        # for streaming specifically, we send the final chunk that contains the usage statistics \n", "        print(f\"Usage: [{message}]\")\n", "    else: \n", "        print(message)\n", "    print(\"-----------------------------------------------------\")"]}, {"cell_type": "markdown", "id": "1833226f-c9ee-4142-86f0-b4841a730583", "metadata": {}, "source": ["## Section 1: Memory Blocks"]}, {"cell_type": "markdown", "id": "a91fec57fb645e8d", "metadata": {}, "source": ["### Creating an agent"]}, {"cell_type": "code", "execution_count": null, "id": "e7792560-0498-48bd-bf6d-2d348b638f99", "metadata": {"height": 251}, "outputs": [], "source": ["agent_state = client.agents.create(\n", "    memory_blocks=[\n", "        {\n", "          \"label\": \"human\",\n", "          \"value\": \"The human's name is <PERSON>.\"\n", "        },\n", "        {\n", "          \"label\": \"persona\",\n", "          \"value\": \"My name is <PERSON>, the all-knowing sentient AI.\"\n", "        }\n", "    ],\n", "    model=\"openai/gpt-4o-mini\",\n", "    embedding=\"openai/text-embedding-3-small\"\n", ")"]}, {"cell_type": "markdown", "id": "61c54c07e6147620", "metadata": {}, "source": ["### Accessing blocks"]}, {"cell_type": "code", "execution_count": null, "id": "88257974-f9fa-41be-95de-8fdf81a929c3", "metadata": {"height": 64}, "outputs": [], "source": ["blocks = client.agents.blocks.list(\n", "    agent_id=agent_state.id,\n", ")"]}, {"cell_type": "markdown", "id": "597b1ecc69f05f4b", "metadata": {}, "source": ["📝 Note: Memory blocks are returned as an unordered list and you may receive blocks in an order different than in the video"]}, {"cell_type": "code", "execution_count": null, "id": "f2ba1488-368f-47f0-9270-5835a9cdd310", "metadata": {"height": 30}, "outputs": [], "source": ["blocks"]}, {"cell_type": "code", "execution_count": null, "id": "7a5caef0-bee2-46fc-8176-75fd63e7b7cc", "metadata": {"height": 47}, "outputs": [], "source": ["# Note: Replace the block_id with the id from the cell above.\n", "block_id='add_block_id_above'"]}, {"cell_type": "code", "execution_count": null, "id": "057a8de8-d663-4a60-a8b2-ee65fd57c784", "metadata": {"height": 30}, "outputs": [], "source": ["client.blocks.retrieve(block_id)"]}, {"cell_type": "code", "execution_count": null, "id": "7e031e6d-d4db-4d01-ae72-3ec1e7f8b9b2", "metadata": {"height": 98}, "outputs": [], "source": ["human_block = client.agents.blocks.retrieve(\n", "    agent_id=agent_state.id,\n", "    block_label=\"human\",\n", ")\n", "human_block"]}, {"cell_type": "markdown", "id": "7bcb0e8d9be7f9ea", "metadata": {}, "source": ["### Accessing block prompt template"]}, {"cell_type": "code", "execution_count": null, "id": "2fe19830-36ea-4b55-b053-bd1bfcbc886d", "metadata": {"height": 64}, "outputs": [], "source": ["client.agents.core_memory.retrieve(\n", "    agent_id=agent_state.id\n", ").prompt_template"]}, {"cell_type": "markdown", "id": "0beb5e47-653c-4a70-8e56-6c10b496debd", "metadata": {}, "source": ["## Section 2: Accessing `AgentState` with <PERSON>ls"]}, {"cell_type": "markdown", "id": "b640ac7cd72d573b", "metadata": {}, "source": ["### Creating tools"]}, {"cell_type": "code", "execution_count": null, "id": "d25db6c8-2510-4a25-ac3e-cc62c79393ad", "metadata": {"height": 98}, "outputs": [], "source": ["def get_agent_id(agent_state: \"AgentState\"):\n", "    \"\"\"\n", "    Query your agent ID field\n", "    \"\"\"\n", "    return agent_state.id"]}, {"cell_type": "code", "execution_count": null, "id": "3fcad314-0ff1-46dc-bbe7-3cbd36ee606e", "metadata": {"height": 30}, "outputs": [], "source": ["get_id_tool = client.tools.upsert_from_function(func=get_agent_id)"]}, {"cell_type": "markdown", "id": "a838d37ae721fc5a", "metadata": {}, "source": ["### Creating agents that use tools"]}, {"cell_type": "code", "execution_count": null, "id": "699328dc-a46b-43b4-82b7-9fe01c31e673", "metadata": {"height": 115}, "outputs": [], "source": ["agent_state = client.agents.create(\n", "    memory_blocks=[],\n", "    model=\"openai/gpt-4o-mini\",\n", "    embedding=\"openai/text-embedding-3-small\",\n", "    tool_ids=[get_id_tool.id]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4853ca14-b9d2-4f64-8cf6-043e7c207286", "metadata": {"height": 217}, "outputs": [], "source": ["response_stream = client.agents.messages.create_stream(\n", "    agent_id=agent_state.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"What is your agent id?\" \n", "        }\n", "    ]\n", ")\n", "\n", "for chunk in response_stream:\n", "    print_message(chunk)"]}, {"cell_type": "markdown", "id": "b8bbd11f-0af2-4ea9-967d-cf9cefc41c56", "metadata": {}, "source": ["## Section 3: Custom Task Queue Memory"]}, {"cell_type": "markdown", "id": "9bc0532d97c5d8ac", "metadata": {}, "source": ["### Creating custom memory management tools"]}, {"cell_type": "code", "execution_count": null, "id": "4641a3a1-cbb6-4c41-a623-742898ee2ecb", "metadata": {"height": 540}, "outputs": [], "source": ["def task_queue_push(agent_state: \"AgentState\", task_description: str):\n", "    \"\"\"\n", "    Push to a task queue stored in core memory.\n", "\n", "    Args:\n", "        task_description (str): A description of the next task you must accomplish.\n", "\n", "    Returns:\n", "        Optional[str]: None is always returned as this function\n", "        does not produce a response.\n", "    \"\"\"\n", "\n", "    from letta_client import Letta\n", "    import json\n", "\n", "    client = Letta(base_url=\"http://localhost:8283\")\n", "\n", "    block = client.agents.blocks.retrieve(\n", "        agent_id=agent_state.id,\n", "        block_label=\"tasks\",\n", "    )\n", "    tasks = json.loads(block.value)\n", "    tasks.append(task_description)\n", "\n", "    # update the block value\n", "    client.agents.blocks.modify(\n", "        agent_id=agent_state.id,\n", "        value=json.dumps(tasks),\n", "        block_label=\"tasks\"\n", "    )\n", "    return None"]}, {"cell_type": "code", "execution_count": null, "id": "b1215378-56fd-4a6a-960d-925cc13ee55f", "metadata": {"height": 540}, "outputs": [], "source": ["def task_queue_pop(agent_state: \"AgentState\"):\n", "    \"\"\"\n", "    Get the next task from the task queue \n", " \n", "    Returns:\n", "        Optional[str]: Remaining tasks in the queue\n", "    \"\"\"\n", "\n", "    from letta_client import Letta\n", "    import json \n", "\n", "    client = Letta(base_url=\"http://localhost:8283\") \n", "\n", "    # get the block \n", "    block = client.agents.blocks.retrieve(\n", "        agent_id=agent_state.id,\n", "        block_label=\"tasks\",\n", "    )\n", "    tasks = json.loads(block.value) \n", "    if len(tasks) == 0: \n", "        return None\n", "    task = tasks[0]\n", "\n", "    # update the block value \n", "    remaining_tasks = json.dumps(tasks[1:])\n", "    client.agents.blocks.modify(\n", "        agent_id=agent_state.id,\n", "        value=remaining_tasks,\n", "        block_label=\"tasks\"\n", "    )\n", "    return f\"Remaining tasks {remaining_tasks}\""]}, {"cell_type": "markdown", "id": "3ae2506e719284c", "metadata": {}, "source": ["### Upserting tools into Letta"]}, {"cell_type": "code", "execution_count": null, "id": "db346bbc-8098-41e1-91d5-9514c4e506e7", "metadata": {"height": 115}, "outputs": [], "source": ["task_queue_pop_tool = client.tools.upsert_from_function(\n", "    func=task_queue_pop\n", ")\n", "task_queue_push_tool = client.tools.upsert_from_function(\n", "    func=task_queue_push\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "7b827919-aa20-4b5d-bbff-de1f58640961", "metadata": {"height": 285}, "outputs": [], "source": ["import json\n", "\n", "task_agent = client.agents.create(\n", "    system=open(\"task_queue_system_prompt.txt\", \"r\").read(),\n", "    memory_blocks=[\n", "        {\n", "          \"label\": \"tasks\",\n", "          \"value\": json.dumps([])\n", "        }\n", "    ],\n", "    model=\"openai/gpt-4o-mini-2024-07-18\",\n", "    embedding=\"openai/text-embedding-3-small\", \n", "    tool_ids=[task_queue_pop_tool.id, task_queue_push_tool.id], \n", "    include_base_tools=False, \n", "    tools=[\"send_message\"]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c535cad8-0390-48cf-bdf5-fbb4aedfff85", "metadata": {"height": 30}, "outputs": [], "source": ["[tool.name for tool in task_agent.tools]"]}, {"cell_type": "code", "execution_count": null, "id": "c74fe8e8-1650-4115-855a-4ee1928c0f75", "metadata": {"height": 30}, "outputs": [], "source": ["client.agents.blocks.retrieve(task_agent.id, block_label=\"tasks\").value"]}, {"cell_type": "markdown", "id": "9a52e6aed3e924c4", "metadata": {}, "source": ["### Using task agent"]}, {"cell_type": "code", "execution_count": null, "id": "61c8288c-c356-4fbc-a445-e7ada1343dfa", "metadata": {"height": 234}, "outputs": [], "source": ["response_stream = client.agents.messages.create_stream(\n", "    agent_id=task_agent.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Add 'start calling me <PERSON>' and \"\n", "            + \"'tell me a haiku about my name' as two seperate tasks.\"\n", "        }\n", "    ]\n", ")\n", "\n", "for chunk in response_stream:\n", "    print_message(chunk)"]}, {"cell_type": "code", "execution_count": null, "id": "b01508fb-ec24-42e0-b644-253f7d29c5b6", "metadata": {"height": 217}, "outputs": [], "source": ["response_stream = client.agents.messages.create_stream(\n", "    agent_id=task_agent.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Complete your tasks\"\n", "        }\n", "    ]\n", ")\n", "\n", "for chunk in response_stream:\n", "    print_message(chunk)"]}, {"cell_type": "markdown", "id": "de749eee9d205e94", "metadata": {}, "source": ["### Retrieving task list"]}, {"cell_type": "code", "execution_count": null, "id": "35967dfb-1fd0-42e5-9610-31dc616fd1b0", "metadata": {"height": 30}, "outputs": [], "source": ["client.agents.blocks.retrieve(block_label=\"tasks\", agent_id=task_agent.id).value"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}