{"cells": [{"cell_type": "markdown", "id": "88d8682d", "metadata": {"ExecuteTime": {"end_time": "2025-04-22T20:46:12.008948Z", "start_time": "2025-04-22T20:46:12.005558Z"}, "height": 30}, "source": ["# Lab 5: <PERSON><PERSON> Rag and External Memory"]}, {"cell_type": "markdown", "id": "5e2ff5b0e4f8ff69", "metadata": {}, "source": ["## Preparation\n", "\n", "<div style=\"background-color:#fff6ff; padding:13px; border-width:3px; border-color:#efe6ef; border-style:solid; border-radius:6px\">\n", "<p> 💻 &nbsp; <b>Access <code>requirements.txt</code> and <code>helper.py</code> files:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Open\"</em>.\n", "\n", "<p> ⬇ &nbsp; <b>Download Notebooks:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Download as\"</em> and select <em>\"Notebook (.ipynb)\"</em>.</p>\n", "\n", "<p> 📒 &nbsp; For more help, please see the <em>\"Appendix – Tips, Help, and Download\"</em> Lesson.</p>\n", "</div>"]}, {"cell_type": "markdown", "id": "93e3d623d9dc2d39", "metadata": {}, "source": ["## Section 0: Setup a Letta client"]}, {"cell_type": "code", "execution_count": null, "id": "7f47ddae-d743-4627-8b5d-7d178e674e18", "metadata": {"height": 64}, "outputs": [], "source": ["from letta_client import Letta\n", "\n", "client = Letta(base_url=\"http://localhost:8283\")"]}, {"cell_type": "code", "execution_count": null, "id": "806154d9-6c23-4f05-8c0f-ba5698d75a70", "metadata": {"height": 200}, "outputs": [], "source": ["def print_message(message):  \n", "    if message.message_type == \"reasoning_message\": \n", "        print(\"🧠 Reasoning: \" + message.reasoning) \n", "    elif message.message_type == \"assistant_message\": \n", "        print(\"🤖 Agent: \" + message.content) \n", "    elif message.message_type == \"tool_call_message\": \n", "        print(\"🔧 Tool Call: \" + message.tool_call.name + \"\\n\" + message.tool_call.arguments)\n", "    elif message.message_type == \"tool_return_message\": \n", "        print(\"🔧 Tool Return: \" + message.tool_return)\n", "    elif message.message_type == \"user_message\": \n", "        print(\"👤 User Message: \" + message.content)"]}, {"cell_type": "markdown", "id": "0d888474-9748-4e6e-a80d-0c13924d6992", "metadata": {}, "source": ["## Section 1: Data Sources"]}, {"cell_type": "markdown", "id": "e20a9722b6ca3729", "metadata": {}, "source": ["### Creating a source"]}, {"cell_type": "code", "execution_count": null, "id": "372a8f61-124d-40ac-830a-2543f5ca836e", "metadata": {"height": 98}, "outputs": [], "source": ["source = client.sources.create(\n", "    name=\"employee_handbook\",\n", "    embedding=\"openai/text-embedding-3-small\"\n", ")\n", "source"]}, {"cell_type": "markdown", "id": "53a1c379f105c65f", "metadata": {}, "source": ["### Uploading a source"]}, {"cell_type": "code", "execution_count": null, "id": "53ea6718-ae10-4413-9ad6-74af1192510e", "metadata": {"height": 81}, "outputs": [], "source": ["job = client.sources.files.upload(\n", "    source_id=source.id,\n", "    file=open(\"handbook.pdf\", \"rb\")\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "64bbd29a-5308-40ac-8181-3d3bd5671498", "metadata": {"height": 30}, "outputs": [], "source": ["job.status"]}, {"cell_type": "markdown", "id": "fb144538c64abce3", "metadata": {}, "source": ["### Viewing job status over time"]}, {"cell_type": "code", "execution_count": null, "id": "19da8f6d-e6b1-4013-8dcd-1a54bf79f383", "metadata": {"height": 132}, "outputs": [], "source": ["import time\n", "from letta_client import JobStatus\n", "\n", "while job.status != 'completed':\n", "    job = client.jobs.retrieve(job.id)\n", "    print(job.status)\n", "    time.sleep(1)"]}, {"cell_type": "markdown", "id": "42d48a6f32d5d4d0", "metadata": {}, "source": ["### Viewing job metadata"]}, {"cell_type": "code", "execution_count": null, "id": "51db2983-fc35-48e4-9e71-8fd7889f7e5c", "metadata": {"height": 30}, "outputs": [], "source": ["job.metadata"]}, {"cell_type": "code", "execution_count": null, "id": "e9a906a0-c157-4558-939f-8fc7ed52b9ca", "metadata": {"height": 81}, "outputs": [], "source": ["passages = client.sources.passages.list(\n", "    source_id=source.id,\n", ")\n", "len(passages)"]}, {"cell_type": "markdown", "id": "e0486dfa6a4026ea", "metadata": {}, "source": ["### Creating an agent and attaching sources"]}, {"cell_type": "code", "execution_count": null, "id": "a32026b6-f820-4ac6-986a-4bc567b2cb1a", "metadata": {"height": 251}, "outputs": [], "source": ["agent_state = client.agents.create(\n", "    memory_blocks=[\n", "        {\n", "          \"label\": \"human\",\n", "          \"value\": \"My name is <PERSON>\"\n", "        },\n", "        {\n", "          \"label\": \"persona\",\n", "          \"value\": \"You are a helpful assistant\"\n", "        }\n", "    ],\n", "    model=\"openai/gpt-4o-mini-2024-07-18\",\n", "    embedding=\"openai/text-embedding-3-small\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "dec14cd1-dadd-4f22-bdb0-b8fae9be8c82", "metadata": {"height": 81}, "outputs": [], "source": ["agent_state = client.agents.sources.attach(\n", "    agent_id=agent_state.id, \n", "    source_id=source.id\n", ")"]}, {"cell_type": "markdown", "id": "8f12c756e74d1e67", "metadata": {}, "source": ["### Viewing agent's attached sources"]}, {"cell_type": "code", "execution_count": null, "id": "e3a8ae36-80b4-4077-a066-77938dbf71f4", "metadata": {"height": 30}, "outputs": [], "source": ["client.agents.sources.list(agent_id=agent_state.id)"]}, {"cell_type": "code", "execution_count": null, "id": "176b2ef9-a601-4887-8767-98e73d6dd4d2", "metadata": {"height": 47}, "outputs": [], "source": ["passages = client.agents.passages.list(agent_id=agent_state.id)\n", "len(passages)"]}, {"cell_type": "markdown", "id": "f85bb1202c672916", "metadata": {}, "source": ["### Messaging agents and referencing attached sources"]}, {"cell_type": "code", "execution_count": null, "id": "55993ee7-9b7a-4a4f-b22d-417f8865124d", "metadata": {"height": 200}, "outputs": [], "source": ["response = client.agents.messages.create(\n", "    agent_id=agent_state.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"Search archival for our company's vacation policies\"\n", "        }\n", "    ]\n", ")\n", "for message in response.messages:\n", "    print_message(message)"]}, {"cell_type": "markdown", "id": "280bcbdb-d516-45c5-a5ef-c6ab82a2fa74", "metadata": {}, "source": ["## Section 2: Connecting Data with Custom Tools"]}, {"cell_type": "markdown", "id": "2ba10ac60598a347", "metadata": {}, "source": ["### Creating a custom tool"]}, {"cell_type": "code", "execution_count": null, "id": "dc3a7c76-71d3-43ea-8e04-0196cf7ab680", "metadata": {"height": 370}, "outputs": [], "source": ["def query_birthday_db(name: str):\n", "    \"\"\"\n", "    This tool queries an external database to\n", "    lookup the birthday of someone given their name.\n", "\n", "    Args:\n", "        name (str): The name to look up\n", "\n", "    Returns:\n", "        birthday (str): The birthday in mm-dd-yyyy format\n", "\n", "    \"\"\"\n", "    my_fake_data = {\n", "        \"bob\": \"03-06-1997\",\n", "        \"sarah\": \"07-06-1993\"\n", "    }\n", "    name = name.lower()\n", "    if name not in my_fake_data:\n", "        return None\n", "    else:\n", "        return my_fake_data[name]"]}, {"cell_type": "code", "execution_count": null, "id": "46df1af0-1575-4f71-bd87-286940fd3915", "metadata": {"height": 30}, "outputs": [], "source": ["birthday_tool = client.tools.upsert_from_function(func=query_birthday_db)"]}, {"cell_type": "markdown", "id": "66ddf294cc61cfaf", "metadata": {}, "source": ["### Creating an agent with access to tools"]}, {"cell_type": "code", "execution_count": null, "id": "41d77187-1d01-412d-b074-ce0dbb72c081", "metadata": {"height": 302}, "outputs": [], "source": ["agent_state = client.agents.create(\n", "    memory_blocks=[\n", "        {\n", "          \"label\": \"human\",\n", "          \"value\": \"My name is <PERSON>\"\n", "        },\n", "        {\n", "          \"label\": \"persona\",\n", "          \"value\": \"You are a agent with access to a birthday_db \" \\\n", "            + \"that you use to lookup information about users' birthdays.\"\n", "        }\n", "    ],\n", "    model=\"openai/gpt-4o-mini-2024-07-18\",\n", "    embedding=\"openai/text-embedding-3-small\",\n", "    tool_ids=[birthday_tool.id],\n", "    #tool_exec_environment_variables={\"DB_KEY\": \"my_key\"}\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "fa3ecbb0-0ca9-40de-b0ac-a8ef4873c784", "metadata": {"height": 217}, "outputs": [], "source": ["# send a message to the agent\n", "response = client.agents.messages.create_stream(\n", "    agent_id=agent_state.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"whens my bday????\"\n", "        }\n", "    ]\n", ")\n", "for message in response:\n", "    print_message(message)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}