#!/usr/bin/env python3
"""
Simple test to check if our modules can be imported
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("🚀 Starting Simple Import Test")
print("=" * 50)

try:
    print("Testing basic imports...")
    
    # Test 1: Basic model imports
    print("\n1. Testing model imports...")
    from models.models import db, User, ExternalDatabase
    print("✓ Basic models imported successfully")
    
    # Test 2: New model imports
    print("\n2. Testing new model imports...")
    from models.models import DatabaseRole, DatabaseSchema, DatabaseEDA
    print("✓ New database models imported successfully")
    
    # Test 3: Utility imports
    print("\n3. Testing utility imports...")
    from utils.database_schema_analyzer import DatabaseSchemaAnalyzer
    print("✓ DatabaseSchemaAnalyzer imported successfully")
    
    from utils.database_eda_engine import DatabaseEDAEngine
    print("✓ DatabaseEDAEngine imported successfully")
    
    from utils.database_semantic_generator import DatabaseSemanticGenerator
    print("✓ DatabaseSemanticGenerator imported successfully")
    
    from utils.database_column_masking_engine import DatabaseColumnMaskingEngine
    print("✓ DatabaseColumnMaskingEngine imported successfully")
    
    # Test 4: API imports
    print("\n4. Testing API imports...")
    from api.database_ingestion_routes import database_ingestion_bp
    print("✓ Database ingestion routes imported successfully")
    
    from api.database_role_routes import database_role_bp
    print("✓ Database role routes imported successfully")
    
    from api.database_user_role_routes import database_user_role_bp
    print("✓ Database user role routes imported successfully")
    
    print("\n🎉 All imports successful!")
    print("✓ The Chat with Databases module is properly structured")
    
except ImportError as e:
    print(f"✗ Import error: {str(e)}")
    import traceback
    traceback.print_exc()
    
except Exception as e:
    print(f"✗ Unexpected error: {str(e)}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 50)
print("Test completed!")
