#!/usr/bin/env python3
"""
Test Database Setup for Chat with Databases Module

This script tests the database setup and checks if our new tables exist.
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_database_connection():
    """Test database connection and check for new tables"""
    print("🗄️ Testing Database Setup")
    print("=" * 50)
    
    try:
        from models.models import db
        from config.database import get_db_connection
        
        print("✓ Database models imported successfully")
        
        # Get database connection
        connection = get_db_connection()
        if connection:
            print("✓ Database connection established")
            
            # Check if our new tables exist
            tables_to_check = [
                'database_roles',
                'database_schemas', 
                'database_tables',
                'database_eda',
                'database_semantic_summaries',
                'training_sessions',
                'training_interactions',
                'database_memory_contexts',
                'user_memory_contexts',
                'enhanced_database_chat_sessions',
                'enhanced_database_chat_messages',
                'database_ingestion_jobs',
                'user_database_roles',
                'role_trainers'
            ]
            
            existing_tables = []
            missing_tables = []
            
            for table_name in tables_to_check:
                try:
                    # Try to query the table
                    cursor = connection.cursor()
                    cursor.execute(f"SELECT 1 FROM {table_name} LIMIT 1")
                    existing_tables.append(table_name)
                    print(f"  ✓ {table_name} exists")
                except Exception as e:
                    missing_tables.append(table_name)
                    print(f"  ✗ {table_name} missing")
            
            print(f"\n📊 Table Status:")
            print(f"  Existing: {len(existing_tables)}/{len(tables_to_check)}")
            print(f"  Missing: {len(missing_tables)}")
            
            if missing_tables:
                print(f"\n❌ Missing tables: {', '.join(missing_tables)}")
                print("💡 Run the migration script to create missing tables:")
                print("   psql -d your_database -f migrations/add_chat_with_databases_models.sql")
            else:
                print("\n🎉 All required tables exist!")
            
            connection.close()
            return len(missing_tables) == 0
            
        else:
            print("✗ Failed to establish database connection")
            return False
            
    except Exception as e:
        print(f"✗ Error testing database: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """Test if we can create model instances"""
    print("\n🏗️ Testing Model Creation")
    print("=" * 30)
    
    try:
        from models.models import (
            DatabaseRole, DatabaseSchema, DatabaseEDA,
            DatabaseSemanticSummary, TrainingSession
        )
        
        # Test creating model instances (without saving to DB)
        role = DatabaseRole(
            name="Test Role",
            description="Test role for validation",
            external_database_id=1,
            created_by=1
        )
        print("✓ DatabaseRole instance created")
        
        schema = DatabaseSchema(
            external_database_id=1,
            schema_data={"test": "data"}
        )
        print("✓ DatabaseSchema instance created")
        
        eda = DatabaseEDA(
            external_database_id=1,
            eda_type="database",
            eda_data={"test": "data"}
        )
        print("✓ DatabaseEDA instance created")
        
        summary = DatabaseSemanticSummary(
            external_database_id=1,
            summary_type="database",
            target_name="test_db",
            llm_generated_summary="Test summary",
            current_summary="Test summary",
            created_by=1
        )
        print("✓ DatabaseSemanticSummary instance created")
        
        session = TrainingSession(
            database_role_id=1,
            trainer_id=1
        )
        print("✓ TrainingSession instance created")
        
        print("\n🎉 All model instances created successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Error creating model instances: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run database setup tests"""
    print("🚀 Starting Database Setup Tests")
    print("=" * 60)
    
    # Test model creation first (doesn't require DB)
    models_ok = test_model_creation()
    
    # Test database connection and tables
    db_ok = test_database_connection()
    
    print("\n" + "=" * 60)
    print("🏁 FINAL RESULTS")
    print("=" * 60)
    
    if models_ok and db_ok:
        print("✅ All database tests passed!")
        print("✅ Database is ready for Chat with Databases module")
    elif models_ok and not db_ok:
        print("⚠️ Models are working but database tables need to be created")
        print("💡 Run the migration script to create the tables")
    else:
        print("❌ Database setup has issues")
        print("💡 Check the error messages above")
    
    print("\n📝 Next Steps:")
    if not db_ok:
        print("1. Run database migrations:")
        print("   psql -d your_database -f migrations/add_chat_with_databases_models.sql")
    print("2. Test with real database connections")
    print("3. Test the complete ingestion pipeline")
    print("4. Create test roles and users")

if __name__ == "__main__":
    main()
