-- Migration: Add Chat with Databases Models
-- Description: Creates all new tables for the comprehensive Chat with Databases system
-- Date: 2024-07-22

-- Create user_database_roles association table (enhanced)
CREATE TABLE IF NOT EXISTS user_database_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    database_role_id INTEGER NOT NULL REFERENCES database_roles(id) ON DELETE CASCADE,
    assigned_by INTEGER REFERENCES users(id),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Create role_trainers association table
CREATE TABLE IF NOT EXISTS role_trainers (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    database_role_id INTEGER NOT NULL REFERENCES database_roles(id) ON DELETE CASCADE,
    assigned_by INTEGER REFERENCES users(id),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Create database_roles table
CREATE TABLE IF NOT EXISTS database_roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    where_conditions TEXT,
    allowed_tables JSON,
    restricted_tables JSON,
    column_masking_rules JSON,
    role_schema JSON,
    is_active BOOLEAN DEFAULT TRUE,
    is_published BOOLEAN DEFAULT FALSE,
    training_status VARCHAR(50) DEFAULT 'not_started',
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create database_schemas table
CREATE TABLE IF NOT EXISTS database_schemas (
    id SERIAL PRIMARY KEY,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    schema_data JSON NOT NULL,
    table_count INTEGER,
    column_count INTEGER,
    analysis_status VARCHAR(50) DEFAULT 'pending',
    analysis_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_analyzed_at TIMESTAMP
);

-- Create database_tables table
CREATE TABLE IF NOT EXISTS database_tables (
    id SERIAL PRIMARY KEY,
    database_schema_id INTEGER NOT NULL REFERENCES database_schemas(id),
    table_name VARCHAR(255) NOT NULL,
    table_schema VARCHAR(255),
    table_type VARCHAR(50),
    row_count BIGINT,
    column_count INTEGER,
    table_size_bytes BIGINT,
    columns_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create database_eda table
CREATE TABLE IF NOT EXISTS database_eda (
    id SERIAL PRIMARY KEY,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    table_id INTEGER REFERENCES database_tables(id),
    eda_type VARCHAR(50) NOT NULL,
    eda_data JSON NOT NULL,
    statistical_summary JSON,
    data_quality_metrics JSON,
    correlation_matrix JSON,
    analysis_status VARCHAR(50) DEFAULT 'pending',
    analysis_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create database_semantic_summaries table
CREATE TABLE IF NOT EXISTS database_semantic_summaries (
    id SERIAL PRIMARY KEY,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    table_id INTEGER REFERENCES database_tables(id),
    database_role_id INTEGER REFERENCES database_roles(id),
    summary_type VARCHAR(50) NOT NULL,
    target_name VARCHAR(255) NOT NULL,
    llm_generated_summary TEXT NOT NULL,
    human_edited_summary TEXT,
    current_summary TEXT NOT NULL,
    business_description TEXT,
    usage_patterns TEXT,
    data_lineage TEXT,
    quality_notes TEXT,
    token_count INTEGER,
    version INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER NOT NULL REFERENCES users(id),
    last_edited_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create training_sessions table
CREATE TABLE IF NOT EXISTS training_sessions (
    id SERIAL PRIMARY KEY,
    database_role_id INTEGER NOT NULL REFERENCES database_roles(id),
    trainer_id INTEGER NOT NULL REFERENCES users(id),
    session_name VARCHAR(255),
    session_description TEXT,
    status VARCHAR(50) DEFAULT 'active',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create training_interactions table
CREATE TABLE IF NOT EXISTS training_interactions (
    id SERIAL PRIMARY KEY,
    training_session_id INTEGER NOT NULL REFERENCES training_sessions(id),
    question TEXT NOT NULL,
    system_answer TEXT NOT NULL,
    sql_query TEXT,
    query_results JSON,
    feedback_rating INTEGER,
    feedback_text TEXT,
    suggested_improvements TEXT,
    summary_corrections JSON,
    applied_corrections BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    feedback_provided_at TIMESTAMP
);

-- Create database_memory_contexts table
CREATE TABLE IF NOT EXISTS database_memory_contexts (
    id SERIAL PRIMARY KEY,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    database_role_id INTEGER REFERENCES database_roles(id),
    core_memory JSON NOT NULL,
    archival_memory JSON,
    recall_memory JSON,
    memory_version INTEGER DEFAULT 1,
    last_updated_by_llm BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_memory_contexts table
CREATE TABLE IF NOT EXISTS user_memory_contexts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    database_role_id INTEGER REFERENCES database_roles(id),
    core_memory JSON NOT NULL,
    archival_memory JSON,
    recall_memory JSON,
    memory_version INTEGER DEFAULT 1,
    last_updated_by_llm BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create memory_updates table
CREATE TABLE IF NOT EXISTS memory_updates (
    id SERIAL PRIMARY KEY,
    database_memory_id INTEGER REFERENCES database_memory_contexts(id),
    user_memory_id INTEGER REFERENCES user_memory_contexts(id),
    update_type VARCHAR(50) NOT NULL,
    update_action VARCHAR(50) NOT NULL,
    old_content JSON,
    new_content JSON NOT NULL,
    update_reason TEXT,
    triggered_by_message_id INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create enhanced_database_chat_sessions table
CREATE TABLE IF NOT EXISTS enhanced_database_chat_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    database_role_id INTEGER NOT NULL REFERENCES database_roles(id),
    session_name VARCHAR(255),
    session_description TEXT,
    user_memory_context_id INTEGER REFERENCES user_memory_contexts(id),
    database_memory_context_id INTEGER REFERENCES database_memory_contexts(id),
    is_active BOOLEAN DEFAULT TRUE,
    is_connected BOOLEAN DEFAULT FALSE,
    connection_established_at TIMESTAMP,
    connection_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create enhanced_database_chat_messages table
CREATE TABLE IF NOT EXISTS enhanced_database_chat_messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER NOT NULL REFERENCES enhanced_database_chat_sessions(id),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    planner_output JSON,
    sql_agent_queries JSON,
    validator_feedback JSON,
    qa_agent_feedback JSON,
    final_sql_query TEXT,
    query_results JSON,
    execution_time_ms INTEGER,
    visualization_data JSON,
    visualization_type VARCHAR(50),
    graph_agent_output JSON,
    memory_updates_triggered JSON,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    token_usage JSON,
    total_processing_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create database_ingestion_jobs table
CREATE TABLE IF NOT EXISTS database_ingestion_jobs (
    id SERIAL PRIMARY KEY,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    job_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    total_tables INTEGER,
    processed_tables INTEGER DEFAULT 0,
    current_step VARCHAR(100),
    schema_analysis_completed BOOLEAN DEFAULT FALSE,
    eda_completed BOOLEAN DEFAULT FALSE,
    summary_generation_completed BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    error_details JSON,
    started_by INTEGER NOT NULL REFERENCES users(id),
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);

-- Create system_configurations table
CREATE TABLE IF NOT EXISTS system_configurations (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(255) NOT NULL UNIQUE,
    config_value JSON NOT NULL,
    config_description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_database_roles_user_id ON user_database_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_database_roles_role_id ON user_database_roles(database_role_id);
CREATE INDEX IF NOT EXISTS idx_user_database_roles_active ON user_database_roles(is_active);

CREATE INDEX IF NOT EXISTS idx_role_trainers_user_id ON role_trainers(user_id);
CREATE INDEX IF NOT EXISTS idx_role_trainers_role_id ON role_trainers(database_role_id);
CREATE INDEX IF NOT EXISTS idx_role_trainers_active ON role_trainers(is_active);

CREATE INDEX IF NOT EXISTS idx_database_roles_external_db ON database_roles(external_database_id);
CREATE INDEX IF NOT EXISTS idx_database_roles_published ON database_roles(is_published);
CREATE INDEX IF NOT EXISTS idx_database_roles_training_status ON database_roles(training_status);

CREATE INDEX IF NOT EXISTS idx_database_schemas_external_db ON database_schemas(external_database_id);
CREATE INDEX IF NOT EXISTS idx_database_tables_schema_id ON database_tables(database_schema_id);
CREATE INDEX IF NOT EXISTS idx_database_eda_external_db ON database_eda(external_database_id);
CREATE INDEX IF NOT EXISTS idx_database_eda_table_id ON database_eda(table_id);

CREATE INDEX IF NOT EXISTS idx_semantic_summaries_external_db ON database_semantic_summaries(external_database_id);
CREATE INDEX IF NOT EXISTS idx_semantic_summaries_role_id ON database_semantic_summaries(database_role_id);
CREATE INDEX IF NOT EXISTS idx_semantic_summaries_active ON database_semantic_summaries(is_active);

CREATE INDEX IF NOT EXISTS idx_training_sessions_role_id ON training_sessions(database_role_id);
CREATE INDEX IF NOT EXISTS idx_training_interactions_session_id ON training_interactions(training_session_id);

CREATE INDEX IF NOT EXISTS idx_database_memory_external_db ON database_memory_contexts(external_database_id);
CREATE INDEX IF NOT EXISTS idx_user_memory_user_id ON user_memory_contexts(user_id);

CREATE INDEX IF NOT EXISTS idx_enhanced_chat_sessions_user_id ON enhanced_database_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_enhanced_chat_sessions_role_id ON enhanced_database_chat_sessions(database_role_id);
CREATE INDEX IF NOT EXISTS idx_enhanced_chat_messages_session_id ON enhanced_database_chat_messages(session_id);

CREATE INDEX IF NOT EXISTS idx_ingestion_jobs_external_db ON database_ingestion_jobs(external_database_id);
CREATE INDEX IF NOT EXISTS idx_ingestion_jobs_status ON database_ingestion_jobs(status);

-- Add unique constraints
ALTER TABLE database_roles ADD CONSTRAINT IF NOT EXISTS unique_role_name_per_database 
    UNIQUE (name, external_database_id);

ALTER TABLE system_configurations ADD CONSTRAINT IF NOT EXISTS unique_config_key 
    UNIQUE (config_key);

-- Add foreign key constraints that might be missing
ALTER TABLE user_database_roles ADD CONSTRAINT IF NOT EXISTS fk_user_database_roles_role_id 
    FOREIGN KEY (database_role_id) REFERENCES database_roles(id) ON DELETE CASCADE;

ALTER TABLE role_trainers ADD CONSTRAINT IF NOT EXISTS fk_role_trainers_role_id 
    FOREIGN KEY (database_role_id) REFERENCES database_roles(id) ON DELETE CASCADE;
